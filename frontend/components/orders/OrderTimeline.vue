<template>
  <div class="order-timeline">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">L<PERSON>ch sử đơn hàng</h3>
      </div>
      <div class="card-content">
        <div v-if="loading" class="flex justify-center py-8">
          <ProgressSpinner />
        </div>
        
        <div v-else-if="timeline.length > 0" class="space-y-4">
          <div
            v-for="(entry, index) in timeline"
            :key="index"
            class="flex gap-4 pb-4"
            :class="{ 'border-b': index < timeline.length - 1 }"
          >
            <!-- Timeline Icon -->
            <div class="flex-shrink-0">
              <div
                class="w-10 h-10 rounded-full flex items-center justify-center text-white"
                :class="getTimelineIconClass(entry.severity)"
              >
                <i :class="entry.icon || getDefaultIcon(entry.action)"></i>
              </div>
            </div>

            <!-- Timeline Content -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between mb-2">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ entry.actionDisplay || entry.action }}
                </h4>
                <span class="text-xs text-gray-500">
                  {{ entry.timestampDisplay || formatDateTime(entry.timestamp) }}
                </span>
              </div>

              <div v-if="entry.user" class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Thực hiện bởi: <span class="font-medium">{{ entry.user }}</span>
              </div>

              <div v-if="entry.reason" class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                <span class="font-medium">Lý do:</span> {{ entry.reason }}
              </div>

              <!-- Change Details -->
              <div v-if="entry.changes && entry.changes.length > 0" class="space-y-2">
                <div
                  v-for="(change, changeIndex) in entry.changes"
                  :key="changeIndex"
                  class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg text-sm"
                >
                  <div class="font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {{ change.fieldDisplayName || change.fieldName }}
                  </div>
                  <div class="flex items-center gap-2">
                    <span class="text-red-600 dark:text-red-400">
                      {{ change.oldValueDisplay || change.oldValue || 'Không có' }}
                    </span>
                    <i class="pi pi-arrow-right text-gray-400"></i>
                    <span class="text-green-600 dark:text-green-400">
                      {{ change.newValueDisplay || change.newValue }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8">
          <i class="pi pi-clock text-4xl text-gray-400 mb-4"></i>
          <p class="text-gray-500">Chưa có lịch sử thay đổi nào</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TimelineEntry } from '~/composables/useOrder'

interface Props {
  orderId: number
  autoLoad?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true
})

const { getOrderHistory } = useOrder()

// Reactive data
const loading = ref(false)
const timeline = ref<TimelineEntry[]>([])

/**
 * Load order timeline
 */
const loadTimeline = async () => {
  if (!props.orderId) return
  
  loading.value = true
  try {
    timeline.value = await getOrderHistory(props.orderId)
  } catch (error) {
    console.error('Error loading order timeline:', error)
  } finally {
    loading.value = false
  }
}

/**
 * Get timeline icon class based on severity
 */
const getTimelineIconClass = (severity?: string): string => {
  const classMap = {
    success: 'bg-green-500',
    info: 'bg-blue-500',
    warn: 'bg-yellow-500',
    error: 'bg-red-500'
  }
  return classMap[severity as keyof typeof classMap] || 'bg-gray-500'
}

/**
 * Get default icon based on action
 */
const getDefaultIcon = (action?: string): string => {
  const iconMap = {
    CREATE: 'pi pi-plus',
    UPDATE: 'pi pi-pencil',
    CANCEL: 'pi pi-times',
    CONFIRM: 'pi pi-check',
    PAYMENT: 'pi pi-credit-card',
    SHIP: 'pi pi-truck',
    DELIVER: 'pi pi-check-circle',
    RETURN: 'pi pi-undo'
  }
  
  if (!action) return 'pi pi-circle'
  
  // Try to match action with icon map
  const upperAction = action.toUpperCase()
  for (const [key, icon] of Object.entries(iconMap)) {
    if (upperAction.includes(key)) {
      return icon
    }
  }
  
  return 'pi pi-circle'
}

/**
 * Refresh timeline
 */
const refresh = () => {
  loadTimeline()
}

// Load timeline on mount if autoLoad is true
onMounted(() => {
  if (props.autoLoad) {
    loadTimeline()
  }
})

// Watch for orderId changes
watch(() => props.orderId, (newOrderId) => {
  if (newOrderId && props.autoLoad) {
    loadTimeline()
  }
})

// Expose refresh method
defineExpose({
  refresh,
  loadTimeline
})
</script>

<style scoped>
.order-timeline {
  /* Custom timeline styles if needed */
}
</style>
