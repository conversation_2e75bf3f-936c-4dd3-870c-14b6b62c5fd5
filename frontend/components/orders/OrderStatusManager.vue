<template>
  <div class="order-status-manager">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Quản lý trạng thái đơn hàng</h3>
      </div>
      <div class="card-content space-y-6">
        <!-- Current Status -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Trạng thái hiện tại
          </label>
          <Tag
            :value="getOrderStatusText(currentStatus)"
            :severity="getOrderStatusSeverity(currentStatus)"
            class="text-base px-4 py-2"
          />
        </div>

        <!-- Status Workflow -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
            Quy trình xử lý đơn hàng
          </label>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(status, index) in statusWorkflow"
              :key="status.value"
              class="flex items-center"
            >
              <!-- Status Step -->
              <div
                class="flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium"
                :class="getStatusStepClass(status.value, index)"
              >
                {{ index + 1 }}
              </div>
              
              <!-- Status Label -->
              <span
                class="ml-2 text-sm"
                :class="getStatusLabelClass(status.value)"
              >
                {{ status.label }}
              </span>

              <!-- Arrow -->
              <i
                v-if="index < statusWorkflow.length - 1"
                class="pi pi-arrow-right mx-3 text-gray-400"
              ></i>
            </div>
          </div>
        </div>

        <!-- Status Update Form -->
        <div v-if="canUpdateStatus" class="border-t pt-6">
          <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Cập nhật trạng thái
          </h4>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Trạng thái mới
              </label>
              <Dropdown
                v-model="newStatus"
                :options="availableStatuses"
                optionLabel="label"
                optionValue="value"
                placeholder="Chọn trạng thái mới"
                class="w-full"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Lý do thay đổi
              </label>
              <Textarea
                v-model="updateReason"
                placeholder="Nhập lý do thay đổi trạng thái"
                rows="3"
                class="w-full"
              />
            </div>

            <!-- Special Fields for Shipping -->
            <div v-if="newStatus === 'DANG_GIAO_HANG'" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Mã vận đơn
                </label>
                <InputText
                  v-model="trackingNumber"
                  placeholder="Nhập mã vận đơn"
                  class="w-full"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Ngày dự kiến giao hàng
                </label>
                <Calendar
                  v-model="expectedDeliveryDate"
                  placeholder="Chọn ngày dự kiến giao hàng"
                  class="w-full"
                  dateFormat="dd/mm/yy"
                  showIcon
                  :minDate="new Date()"
                />
              </div>
            </div>

            <div class="flex gap-3">
              <Button
                label="Cập nhật trạng thái"
                @click="updateStatus"
                :loading="updating"
                :disabled="!newStatus || newStatus === currentStatus"
              />
              <Button
                label="Hủy"
                outlined
                @click="resetForm"
              />
            </div>
          </div>
        </div>

        <!-- Payment Status Management -->
        <div v-if="showPaymentManagement" class="border-t pt-6">
          <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Quản lý thanh toán
          </h4>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Trạng thái thanh toán hiện tại
              </label>
              <Tag
                :value="getPaymentStatusText(currentPaymentStatus)"
                :severity="getPaymentStatusSeverity(currentPaymentStatus)"
                class="text-base px-4 py-2"
              />
            </div>

            <div v-if="currentPaymentStatus === 'CHUA_THANH_TOAN'">
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phương thức thanh toán
                  </label>
                  <Dropdown
                    v-model="paymentMethod"
                    :options="paymentMethods"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="Chọn phương thức thanh toán"
                    class="w-full"
                  />
                </div>

                <Button
                  label="Xác nhận thanh toán"
                  @click="confirmPayment"
                  :loading="confirmingPayment"
                  :disabled="!paymentMethod"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { OrderStatus, PaymentStatus } from '~/composables/useOrder'

interface Props {
  orderId: number
  currentStatus: OrderStatus
  currentPaymentStatus: PaymentStatus
  canUpdateStatus?: boolean
  showPaymentManagement?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  canUpdateStatus: true,
  showPaymentManagement: true
})

const emit = defineEmits<{
  statusUpdated: [status: OrderStatus]
  paymentConfirmed: [paymentMethod: string]
}>()

const { 
  updateOrderStatus,
  confirmPayment,
  getOrderStatusText,
  getPaymentStatusText,
  getOrderStatusSeverity,
  getPaymentStatusSeverity
} = useOrder()
const toast = useToast()

// Reactive data
const updating = ref(false)
const confirmingPayment = ref(false)
const newStatus = ref<OrderStatus | null>(null)
const updateReason = ref('')
const trackingNumber = ref('')
const expectedDeliveryDate = ref<Date | null>(null)
const paymentMethod = ref('')

// Status workflow
const statusWorkflow = [
  { label: 'Chờ xác nhận', value: 'CHO_XAC_NHAN' },
  { label: 'Đã xác nhận', value: 'DA_XAC_NHAN' },
  { label: 'Đang chuẩn bị', value: 'DANG_CHUAN_BI' },
  { label: 'Đang giao hàng', value: 'DANG_GIAO_HANG' },
  { label: 'Đã giao hàng', value: 'DA_GIAO_HANG' }
]

// Payment methods
const paymentMethods = [
  { label: 'Tiền mặt', value: 'TIEN_MAT' },
  { label: 'Chuyển khoản', value: 'CHUYEN_KHOAN' },
  { label: 'VNPay', value: 'VNPAY' },
  { label: 'Thẻ tín dụng', value: 'THE_TIN_DUNG' }
]

/**
 * Get available statuses based on current status
 */
const availableStatuses = computed(() => {
  const currentIndex = statusWorkflow.findIndex(s => s.value === props.currentStatus)
  if (currentIndex === -1) return statusWorkflow
  
  // Can move to next status or cancel
  const nextStatuses = statusWorkflow.slice(currentIndex + 1)
  
  // Add cancel option if not already cancelled or delivered
  if (!['DA_HUY', 'DA_GIAO_HANG'].includes(props.currentStatus)) {
    nextStatuses.push({ label: 'Hủy đơn hàng', value: 'DA_HUY' })
  }
  
  return nextStatuses
})

/**
 * Get status step class
 */
const getStatusStepClass = (status: string, index: number): string => {
  const currentIndex = statusWorkflow.findIndex(s => s.value === props.currentStatus)
  
  if (index <= currentIndex) {
    return 'bg-green-500 text-white'
  } else {
    return 'bg-gray-300 text-gray-600'
  }
}

/**
 * Get status label class
 */
const getStatusLabelClass = (status: string): string => {
  const currentIndex = statusWorkflow.findIndex(s => s.value === props.currentStatus)
  const statusIndex = statusWorkflow.findIndex(s => s.value === status)
  
  if (statusIndex <= currentIndex) {
    return 'font-medium text-green-600'
  } else {
    return 'text-gray-500'
  }
}

/**
 * Update order status
 */
const updateStatus = async () => {
  if (!newStatus.value) return
  
  updating.value = true
  try {
    await updateOrderStatus(props.orderId, newStatus.value, updateReason.value)
    
    toast.add({
      severity: 'success',
      summary: 'Thành công',
      detail: 'Cập nhật trạng thái đơn hàng thành công',
      life: 3000
    })
    
    emit('statusUpdated', newStatus.value)
    resetForm()
  } catch (error) {
    // Error is handled by the composable
  } finally {
    updating.value = false
  }
}

/**
 * Confirm payment
 */
const confirmPaymentAction = async () => {
  if (!paymentMethod.value) return
  
  confirmingPayment.value = true
  try {
    await confirmPayment(props.orderId, paymentMethod.value)
    
    toast.add({
      severity: 'success',
      summary: 'Thành công',
      detail: 'Xác nhận thanh toán thành công',
      life: 3000
    })
    
    emit('paymentConfirmed', paymentMethod.value)
    paymentMethod.value = ''
  } catch (error) {
    // Error is handled by the composable
  } finally {
    confirmingPayment.value = false
  }
}

/**
 * Reset form
 */
const resetForm = () => {
  newStatus.value = null
  updateReason.value = ''
  trackingNumber.value = ''
  expectedDeliveryDate.value = null
}
</script>

<style scoped>
.order-status-manager {
  /* Custom styles if needed */
}
</style>
