<template>
  <Card>
    <template #content>
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Basic Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium mb-2 required">
              Mã Voucher
            </label>
            <div class="flex gap-2">
              <InputText
                v-model="form.maPhieuGiamGia"
                placeholder="Nhập mã voucher"
                class="flex-1"
                :class="{ 'p-invalid': errors.maPhieuGiamGia }"
                :disabled="isEdit"
              />
              <Button
                v-if="!isEdit"
                icon="pi pi-refresh"
                severity="secondary"
                @click="generateCode"
                v-tooltip="'Tạo mã tự động'"
              />
            </div>
            <small v-if="errors.maPhieuGiamGia" class="p-error">
              {{ errors.maPhieuGiamGia }}
            </small>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2 required">
              Loại giảm giá
            </label>
            <Select
              v-model="form.loaiGiamGia"
              :options="discountTypeOptions"
              option-label="label"
              option-value="value"
              placeholder="Chọn loại giảm giá"
              class="w-full"
              :class="{ 'p-invalid': errors.loaiGiamGia }"
            />
            <small v-if="errors.loaiGiamGia" class="p-error">
              {{ errors.loaiGiamGia }}
            </small>
          </div>
        </div>

        <!-- Discount Value -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium mb-2 required">
              {{ form.loaiGiamGia === 'PHAN_TRAM' ? 'Phần trăm giảm (%)' : 'Số tiền giảm (VND)' }}
            </label>
            <InputNumber
              v-model="form.giaTriGiam"
              :placeholder="form.loaiGiamGia === 'PHAN_TRAM' ? 'Nhập phần trăm (1-100)' : 'Nhập số tiền'"
              :min="form.loaiGiamGia === 'PHAN_TRAM' ? 0.01 : 1000"
              :max="form.loaiGiamGia === 'PHAN_TRAM' ? 100 : undefined"
              :step="form.loaiGiamGia === 'PHAN_TRAM' ? 0.01 : 1000"
              :currency="form.loaiGiamGia === 'SO_TIEN_CO_DINH' ? 'VND' : undefined"
              :mode="form.loaiGiamGia === 'SO_TIEN_CO_DINH' ? 'currency' : 'decimal'"
              locale="vi-VN"
              class="w-full"
              :class="{ 'p-invalid': errors.giaTriGiam }"
            />
            <small v-if="errors.giaTriGiam" class="p-error">
              {{ errors.giaTriGiam }}
            </small>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">
              Giá trị đơn hàng tối thiểu (VND)
            </label>
            <InputNumber
              v-model="form.giaTriDonHangToiThieu"
              placeholder="Nhập giá trị tối thiểu (tùy chọn)"
              :min="0"
              :step="10000"
              mode="currency"
              currency="VND"
              locale="vi-VN"
              class="w-full"
              :class="{ 'p-invalid': errors.giaTriDonHangToiThieu }"
            />
            <small v-if="errors.giaTriDonHangToiThieu" class="p-error">
              {{ errors.giaTriDonHangToiThieu }}
            </small>
            <small class="text-gray-500">
              Để trống nếu không có giới hạn
            </small>
          </div>
        </div>

        <!-- Date Range -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium mb-2 required">
              Ngày bắt đầu
            </label>
            <DatePicker
              v-model="form.ngayBatDau"
              placeholder="Chọn ngày bắt đầu"
              :min-date="new Date()"
              show-time
              hour-format="24"
              class="w-full"
              :class="{ 'p-invalid': errors.ngayBatDau }"
            />
            <small v-if="errors.ngayBatDau" class="p-error">
              {{ errors.ngayBatDau }}
            </small>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2 required">
              Ngày kết thúc
            </label>
            <DatePicker
              v-model="form.ngayKetThuc"
              placeholder="Chọn ngày kết thúc"
              :min-date="form.ngayBatDau || new Date()"
              show-time
              hour-format="24"
              class="w-full"
              :class="{ 'p-invalid': errors.ngayKetThuc }"
            />
            <small v-if="errors.ngayKetThuc" class="p-error">
              {{ errors.ngayKetThuc }}
            </small>
          </div>
        </div>

        <!-- Quantity -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium mb-2 required">
              Số lượng voucher
            </label>
            <InputNumber
              v-model="form.soLuongBanDau"
              placeholder="Nhập số lượng"
              :min="1"
              :max="10000"
              class="w-full"
              :class="{ 'p-invalid': errors.soLuongBanDau }"
            />
            <small v-if="errors.soLuongBanDau" class="p-error">
              {{ errors.soLuongBanDau }}
            </small>
          </div>

          <div v-if="isEdit">
            <label class="block text-sm font-medium mb-2">
              Số lượng đã sử dụng
            </label>
            <InputNumber
              :model-value="form.soLuongDaDung"
              disabled
              class="w-full"
            />
            <small class="text-gray-500">
              Không thể chỉnh sửa số lượng đã sử dụng
            </small>
          </div>
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium mb-2">
            Mô tả
          </label>
          <Textarea
            v-model="form.moTa"
            placeholder="Nhập mô tả voucher (tùy chọn)"
            rows="4"
            class="w-full"
            :class="{ 'p-invalid': errors.moTa }"
          />
          <small v-if="errors.moTa" class="p-error">
            {{ errors.moTa }}
          </small>
        </div>

        <!-- Customer Assignment -->
        <div>
          <label class="block text-sm font-medium mb-2">
            Phân quyền khách hàng
          </label>
          <div class="flex items-center gap-4 mb-3">
            <div class="flex items-center">
              <RadioButton
                v-model="customerAssignmentType"
                input-id="public"
                value="public"
              />
              <label for="public" class="ml-2">Công khai (tất cả khách hàng)</label>
            </div>
            <div class="flex items-center">
              <RadioButton
                v-model="customerAssignmentType"
                input-id="private"
                value="private"
              />
              <label for="private" class="ml-2">Riêng tư (khách hàng cụ thể)</label>
            </div>
          </div>

          <div v-if="customerAssignmentType === 'private'" class="mt-4">
            <MultiSelect
              v-model="form.danhSachNguoiDung"
              :options="customers"
              option-label="hoTen"
              option-value="id"
              placeholder="Chọn khách hàng"
              filter
              class="w-full"
              :loading="loadingCustomers"
            >
              <template #option="{ option }">
                <div class="flex items-center gap-2">
                  <Avatar
                    :label="option.hoTen.charAt(0)"
                    size="small"
                    shape="circle"
                  />
                  <div>
                    <div class="font-medium">{{ option.hoTen }}</div>
                    <div class="text-sm text-gray-500">{{ option.email }}</div>
                  </div>
                </div>
              </template>
            </MultiSelect>
            <small class="text-gray-500">
              Chọn khách hàng có thể sử dụng voucher này
            </small>
          </div>
        </div>

        <!-- Audit Reason (for edit) -->
        <div v-if="isEdit">
          <label class="block text-sm font-medium mb-2">
            Lý do thay đổi
          </label>
          <Textarea
            v-model="form.lyDoThayDoi"
            placeholder="Nhập lý do thay đổi (tùy chọn)"
            rows="2"
            class="w-full"
          />
          <small class="text-gray-500">
            Lý do này sẽ được ghi vào lịch sử thay đổi
          </small>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end gap-3 pt-6 border-t">
          <Button
            type="button"
            label="Hủy"
            severity="secondary"
            @click="$emit('cancel')"
          />
          <Button
            type="submit"
            :label="isEdit ? 'Cập nhật' : 'Tạo voucher'"
            :loading="submitting"
            :disabled="!isFormValid"
          />
        </div>
      </form>
    </template>
  </Card>
</template>

<script setup lang="ts">
import type { Voucher } from '~/composables/useVoucher'

interface Props {
  voucher?: Voucher
  isEdit?: boolean
}

interface Emits {
  (e: 'submit', data: Partial<Voucher>): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false
})

const emit = defineEmits<Emits>()

// Store and composables
const voucherStore = useVoucherStore()
const toast = useToast()

// Destructure store methods
const { generateVoucherCode } = voucherStore

// State
const submitting = ref(false)
const loadingCustomers = ref(false)
const customers = ref([])

// Form data
const form = reactive({
  maPhieuGiamGia: '',
  loaiGiamGia: 'PHAN_TRAM' as 'PHAN_TRAM' | 'SO_TIEN_CO_DINH',
  giaTriGiam: 0,
  giaTriDonHangToiThieu: null as number | null,
  ngayBatDau: null as Date | null,
  ngayKetThuc: null as Date | null,
  moTa: '',
  soLuongBanDau: 1,
  soLuongDaDung: 0,
  danhSachNguoiDung: [] as number[],
  lyDoThayDoi: ''
})

// Customer assignment type
const customerAssignmentType = ref<'public' | 'private'>('public')

// Form validation
const errors = reactive({
  maPhieuGiamGia: '',
  loaiGiamGia: '',
  giaTriGiam: '',
  giaTriDonHangToiThieu: '',
  ngayBatDau: '',
  ngayKetThuc: '',
  moTa: '',
  soLuongBanDau: ''
})

// Options
const discountTypeOptions = [
  { label: 'Phần trăm (%)', value: 'PHAN_TRAM' },
  { label: 'Số tiền cố định (VND)', value: 'SO_TIEN_CO_DINH' }
]

// Computed
const isFormValid = computed(() => {
  return form.maPhieuGiamGia &&
         form.loaiGiamGia &&
         form.giaTriGiam > 0 &&
         form.ngayBatDau &&
         form.ngayKetThuc &&
         form.soLuongBanDau > 0 &&
         !Object.values(errors).some(error => error)
})

// Methods
const generateCode = () => {
  form.maPhieuGiamGia = generateVoucherCode('VOUCHER')
}

const validateForm = () => {
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })

  // Validate voucher code
  if (!form.maPhieuGiamGia) {
    errors.maPhieuGiamGia = 'Mã voucher là bắt buộc'
  } else if (form.maPhieuGiamGia.length < 3) {
    errors.maPhieuGiamGia = 'Mã voucher phải có ít nhất 3 ký tự'
  } else if (!/^[A-Z0-9_-]+$/.test(form.maPhieuGiamGia)) {
    errors.maPhieuGiamGia = 'Mã voucher chỉ được chứa chữ hoa, số, dấu gạch dưới và gạch ngang'
  }

  // Validate discount type
  if (!form.loaiGiamGia) {
    errors.loaiGiamGia = 'Loại giảm giá là bắt buộc'
  }

  // Validate discount value
  if (!form.giaTriGiam || form.giaTriGiam <= 0) {
    errors.giaTriGiam = 'Giá trị giảm phải lớn hơn 0'
  } else if (form.loaiGiamGia === 'PHAN_TRAM' && form.giaTriGiam > 100) {
    errors.giaTriGiam = 'Phần trăm giảm không được vượt quá 100%'
  } else if (form.loaiGiamGia === 'SO_TIEN_CO_DINH' && form.giaTriGiam < 1000) {
    errors.giaTriGiam = 'Số tiền giảm phải ít nhất 1,000 VND'
  }

  // Validate minimum order value
  if (form.giaTriDonHangToiThieu !== null && form.giaTriDonHangToiThieu < 0) {
    errors.giaTriDonHangToiThieu = 'Giá trị đơn hàng tối thiểu không được âm'
  }

  // Validate dates
  if (!form.ngayBatDau) {
    errors.ngayBatDau = 'Ngày bắt đầu là bắt buộc'
  }

  if (!form.ngayKetThuc) {
    errors.ngayKetThuc = 'Ngày kết thúc là bắt buộc'
  } else if (form.ngayBatDau && form.ngayKetThuc <= form.ngayBatDau) {
    errors.ngayKetThuc = 'Ngày kết thúc phải sau ngày bắt đầu'
  }

  // Validate quantity
  if (!form.soLuongBanDau || form.soLuongBanDau <= 0) {
    errors.soLuongBanDau = 'Số lượng voucher phải lớn hơn 0'
  } else if (form.soLuongBanDau > 10000) {
    errors.soLuongBanDau = 'Số lượng voucher không được vượt quá 10,000'
  }

  return !Object.values(errors).some(error => error)
}

const handleSubmit = async () => {
  if (!validateForm()) {
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Vui lòng kiểm tra lại thông tin form',
      life: 5000
    })
    return
  }

  submitting.value = true

  try {
    // Prepare voucher data
    const voucherData = {
      ...form,
      ngayBatDau: form.ngayBatDau!.toISOString(),
      ngayKetThuc: form.ngayKetThuc!.toISOString(),
      danhSachNguoiDung: customerAssignmentType.value === 'public' ? [] : form.danhSachNguoiDung
    }

    emit('submit', voucherData)
  } finally {
    submitting.value = false
  }
}

const loadCustomers = async () => {
  loadingCustomers.value = true
  try {
    // This would be replaced with actual customer API call
    const { get } = useApi()
    const data = await get('/nguoi-dung?role=KHACH_HANG')
    customers.value = data || []
  } catch (error) {
    console.error('Error loading customers:', error)
  } finally {
    loadingCustomers.value = false
  }
}

// Watch customer assignment type
watch(customerAssignmentType, (newType) => {
  if (newType === 'public') {
    form.danhSachNguoiDung = []
  }
})

// Initialize form with voucher data if editing
watch(() => props.voucher, (voucher) => {
  if (voucher && props.isEdit) {
    Object.assign(form, {
      maPhieuGiamGia: voucher.maPhieuGiamGia,
      loaiGiamGia: voucher.loaiGiamGia,
      giaTriGiam: voucher.giaTriGiam,
      giaTriDonHangToiThieu: voucher.giaTriDonHangToiThieu,
      ngayBatDau: new Date(voucher.ngayBatDau),
      ngayKetThuc: new Date(voucher.ngayKetThuc),
      moTa: voucher.moTa || '',
      soLuongBanDau: voucher.soLuongBanDau,
      soLuongDaDung: voucher.soLuongDaDung,
      danhSachNguoiDung: voucher.danhSachNguoiDung || [],
      lyDoThayDoi: ''
    })

    customerAssignmentType.value = voucher.danhSachNguoiDung && voucher.danhSachNguoiDung.length > 0 ? 'private' : 'public'
  }
}, { immediate: true })

// Initialize
onMounted(async () => {
  await loadCustomers()
  
  if (!props.isEdit) {
    // Set default dates for new voucher
    const now = new Date()
    form.ngayBatDau = now
    form.ngayKetThuc = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days later
  }
})
</script>

<style scoped>
.required::after {
  content: ' *';
  @apply text-red-500;
}

:deep(.p-inputnumber-input) {
  @apply text-right;
}

:deep(.p-calendar) {
  @apply w-full;
}

:deep(.p-multiselect) {
  @apply min-h-[42px];
}
</style>
