<template>
  <div class="voucher-analytics">
    <!-- Analytics Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
          Phân tích Voucher
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          Thống kê hiệu suất và xu hướng sử dụng voucher
        </p>
      </div>
      <div class="flex gap-3">
        <Select
          v-model="selectedPeriod"
          :options="periodOptions"
          option-label="label"
          option-value="value"
          placeholder="Chọn khoảng thời gian"
          class="w-48"
        />
        <Button
          icon="pi pi-refresh"
          label="Làm mới"
          severity="secondary"
          @click="refreshAnalytics"
          :loading="loading"
        />
      </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card class="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ analytics.totalVouchers }}</div>
              <div class="text-blue-100">Tổng Voucher</div>
              <div class="text-sm text-blue-200 mt-1">
                <i class="pi pi-arrow-up mr-1"></i>
                +{{ analytics.voucherGrowth }}% so với kỳ trước
              </div>
            </div>
            <i class="pi pi-ticket text-3xl text-blue-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-green-500 to-green-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ analytics.totalUsage }}</div>
              <div class="text-green-100">Lượt Sử Dụng</div>
              <div class="text-sm text-green-200 mt-1">
                <i class="pi pi-arrow-up mr-1"></i>
                +{{ analytics.usageGrowth }}% so với kỳ trước
              </div>
            </div>
            <i class="pi pi-chart-line text-3xl text-green-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ formatCurrency(analytics.totalDiscount) }}</div>
              <div class="text-purple-100">Tổng Giảm Giá</div>
              <div class="text-sm text-purple-200 mt-1">
                <i class="pi pi-arrow-up mr-1"></i>
                +{{ analytics.discountGrowth }}% so với kỳ trước
              </div>
            </div>
            <i class="pi pi-money-bill text-3xl text-purple-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ analytics.averageUsageRate.toFixed(1) }}%</div>
              <div class="text-orange-100">Tỷ Lệ Sử Dụng TB</div>
              <div class="text-sm text-orange-200 mt-1">
                <i class="pi pi-arrow-up mr-1"></i>
                +{{ analytics.usageRateGrowth }}% so với kỳ trước
              </div>
            </div>
            <i class="pi pi-percentage text-3xl text-orange-200"></i>
          </div>
        </template>
      </Card>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Usage Trend Chart -->
      <Card>
        <template #header>
          <div class="p-4 border-b">
            <h3 class="text-lg font-semibold">Xu hướng sử dụng Voucher</h3>
          </div>
        </template>
        <template #content>
          <Chart
            type="line"
            :data="usageTrendData"
            :options="chartOptions"
            class="h-80"
          />
        </template>
      </Card>

      <!-- Voucher Type Distribution -->
      <Card>
        <template #header>
          <div class="p-4 border-b">
            <h3 class="text-lg font-semibold">Phân bố loại Voucher</h3>
          </div>
        </template>
        <template #content>
          <Chart
            type="doughnut"
            :data="typeDistributionData"
            :options="doughnutOptions"
            class="h-80"
          />
        </template>
      </Card>
    </div>

    <!-- Top Performing Vouchers -->
    <Card class="mb-8">
      <template #header>
        <div class="p-4 border-b">
          <h3 class="text-lg font-semibold">Top Voucher hiệu quả nhất</h3>
        </div>
      </template>
      <template #content>
        <DataTable
          :value="topVouchers"
          :loading="loading"
          class="p-datatable-sm"
        >
          <Column field="maPhieuGiamGia" header="Mã Voucher">
            <template #body="{ data }">
              <div class="font-mono font-semibold text-blue-600">
                {{ data.maPhieuGiamGia }}
              </div>
            </template>
          </Column>

          <Column field="loaiGiamGia" header="Loại">
            <template #body="{ data }">
              <Tag
                :value="data.loaiGiamGia === 'PHAN_TRAM' ? 'Phần trăm' : 'Số tiền'"
                :severity="data.loaiGiamGia === 'PHAN_TRAM' ? 'info' : 'success'"
              />
            </template>
          </Column>

          <Column field="usageRate" header="Tỷ lệ sử dụng">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <ProgressBar
                  :value="data.usageRate"
                  class="w-20"
                  style="height: 6px"
                />
                <span class="text-sm font-semibold">{{ data.usageRate.toFixed(1) }}%</span>
              </div>
            </template>
          </Column>

          <Column field="totalUsage" header="Lượt sử dụng">
            <template #body="{ data }">
              <div class="text-center font-semibold">
                {{ data.totalUsage }}
              </div>
            </template>
          </Column>

          <Column field="totalDiscount" header="Tổng giảm giá">
            <template #body="{ data }">
              <div class="font-semibold text-green-600">
                {{ formatCurrency(data.totalDiscount) }}
              </div>
            </template>
          </Column>

          <Column field="effectiveness" header="Hiệu quả">
            <template #body="{ data }">
              <Tag
                :value="data.effectiveness"
                :severity="getEffectivenessSeverity(data.effectiveness)"
              />
            </template>
          </Column>
        </DataTable>
      </template>
    </Card>

    <!-- Status Distribution -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Status Chart -->
      <Card>
        <template #header>
          <div class="p-4 border-b">
            <h3 class="text-lg font-semibold">Phân bố trạng thái Voucher</h3>
          </div>
        </template>
        <template #content>
          <Chart
            type="bar"
            :data="statusDistributionData"
            :options="barChartOptions"
            class="h-64"
          />
        </template>
      </Card>

      <!-- Performance Metrics -->
      <Card>
        <template #header>
          <div class="p-4 border-b">
            <h3 class="text-lg font-semibold">Chỉ số hiệu suất</h3>
          </div>
        </template>
        <template #content>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="font-medium">Voucher hiệu quả cao:</span>
              <div class="flex items-center gap-2">
                <span class="font-bold text-green-600">{{ analytics.highPerformanceCount }}</span>
                <span class="text-sm text-gray-500">
                  ({{ ((analytics.highPerformanceCount / analytics.totalVouchers) * 100).toFixed(1) }}%)
                </span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="font-medium">Voucher hiệu quả thấp:</span>
              <div class="flex items-center gap-2">
                <span class="font-bold text-red-600">{{ analytics.lowPerformanceCount }}</span>
                <span class="text-sm text-gray-500">
                  ({{ ((analytics.lowPerformanceCount / analytics.totalVouchers) * 100).toFixed(1) }}%)
                </span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="font-medium">Voucher sắp hết hạn:</span>
              <div class="flex items-center gap-2">
                <span class="font-bold text-orange-600">{{ analytics.expiringCount }}</span>
                <span class="text-sm text-gray-500">(trong 7 ngày tới)</span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="font-medium">Tỷ lệ chuyển đổi:</span>
              <div class="flex items-center gap-2">
                <span class="font-bold text-blue-600">{{ analytics.conversionRate.toFixed(2) }}%</span>
                <span class="text-sm text-gray-500">(voucher → đơn hàng)</span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="font-medium">Giá trị đơn hàng trung bình:</span>
              <div class="flex items-center gap-2">
                <span class="font-bold text-purple-600">{{ formatCurrency(analytics.averageOrderValue) }}</span>
                <span class="text-sm text-gray-500">(có voucher)</span>
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
// Store and composables
const voucherStore = useVoucherStore()
const { generateRevenueData } = useChart()

// Destructure store
const {
  vouchers,
  voucherStats,
  loading,
  getVoucherUsageRate,
  getVoucherEffectiveness,
  refreshVouchers
} = voucherStore

// State
const selectedPeriod = ref('30d')
const analytics = ref({
  totalVouchers: 0,
  totalUsage: 0,
  totalDiscount: 0,
  averageUsageRate: 0,
  voucherGrowth: 0,
  usageGrowth: 0,
  discountGrowth: 0,
  usageRateGrowth: 0,
  highPerformanceCount: 0,
  lowPerformanceCount: 0,
  expiringCount: 0,
  conversionRate: 0,
  averageOrderValue: 0
})

// Options
const periodOptions = [
  { label: '7 ngày qua', value: '7d' },
  { label: '30 ngày qua', value: '30d' },
  { label: '90 ngày qua', value: '90d' },
  { label: 'Năm nay', value: 'year' }
]

// Chart data
const usageTrendData = computed(() => ({
  labels: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
  datasets: [
    {
      label: 'Lượt sử dụng',
      data: [12, 19, 15, 25, 22, 30, 28],
      borderColor: '#3B82F6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
      fill: true
    },
    {
      label: 'Voucher mới',
      data: [5, 8, 6, 10, 9, 12, 11],
      borderColor: '#10B981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4,
      fill: true
    }
  ]
}))

const typeDistributionData = computed(() => ({
  labels: ['Phần trăm', 'Số tiền cố định'],
  datasets: [
    {
      data: [
        vouchers.value.filter(v => v.loaiGiamGia === 'PHAN_TRAM').length,
        vouchers.value.filter(v => v.loaiGiamGia === 'SO_TIEN_CO_DINH').length
      ],
      backgroundColor: ['#3B82F6', '#10B981'],
      borderWidth: 0
    }
  ]
}))

const statusDistributionData = computed(() => ({
  labels: ['Chưa diễn ra', 'Đang diễn ra', 'Đã kết thúc', 'Đã hủy'],
  datasets: [
    {
      label: 'Số lượng',
      data: [
        vouchers.value.filter(v => v.trangThai === 'CHUA_DIEN_RA').length,
        vouchers.value.filter(v => v.trangThai === 'DA_DIEN_RA').length,
        vouchers.value.filter(v => v.trangThai === 'DA_KET_THUC').length,
        vouchers.value.filter(v => v.trangThai === 'DA_HUY').length
      ],
      backgroundColor: ['#6B7280', '#10B981', '#F59E0B', '#EF4444'],
      borderWidth: 0
    }
  ]
}))

const topVouchers = computed(() => {
  return vouchers.value
    .map(voucher => ({
      ...voucher,
      usageRate: getVoucherUsageRate(voucher),
      totalUsage: voucher.soLuongDaDung,
      totalDiscount: voucher.soLuongDaDung * voucher.giaTriGiam,
      effectiveness: getVoucherEffectiveness(voucher)
    }))
    .sort((a, b) => b.usageRate - a.usageRate)
    .slice(0, 10)
})

// Chart options
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const
    }
  },
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const
    }
  }
}

const barChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

// Methods
const refreshAnalytics = async () => {
  await refreshVouchers(true)
  calculateAnalytics()
}

const calculateAnalytics = () => {
  const now = new Date()
  const periodDays = selectedPeriod.value === '7d' ? 7 :
                   selectedPeriod.value === '30d' ? 30 :
                   selectedPeriod.value === '90d' ? 90 : 365

  const periodStart = new Date(now.getTime() - periodDays * 24 * 60 * 60 * 1000)

  // Filter vouchers by period
  const periodVouchers = vouchers.value.filter(voucher => {
    const createdDate = new Date(voucher.ngayTao || voucher.ngayBatDau)
    return createdDate >= periodStart
  })

  // Calculate metrics
  analytics.value = {
    totalVouchers: periodVouchers.length,
    totalUsage: periodVouchers.reduce((sum, v) => sum + v.soLuongDaDung, 0),
    totalDiscount: periodVouchers.reduce((sum, v) => sum + (v.soLuongDaDung * v.giaTriGiam), 0),
    averageUsageRate: periodVouchers.length > 0 ?
      periodVouchers.reduce((sum, v) => sum + getVoucherUsageRate(v), 0) / periodVouchers.length : 0,
    voucherGrowth: Math.floor(Math.random() * 20) + 5, // Mock data
    usageGrowth: Math.floor(Math.random() * 30) + 10,
    discountGrowth: Math.floor(Math.random() * 25) + 8,
    usageRateGrowth: Math.floor(Math.random() * 15) + 3,
    highPerformanceCount: vouchers.value.filter(v => getVoucherUsageRate(v) >= 80).length,
    lowPerformanceCount: vouchers.value.filter(v => getVoucherUsageRate(v) < 30).length,
    expiringCount: vouchers.value.filter(v => {
      const endDate = new Date(v.ngayKetThuc)
      const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      return daysLeft <= 7 && daysLeft > 0
    }).length,
    conversionRate: Math.random() * 5 + 2, // Mock data
    averageOrderValue: 18500000 + Math.random() * 5000000 // Mock data
  }
}

const getEffectivenessSeverity = (effectiveness: string) => {
  switch (effectiveness) {
    case 'Hiệu quả cao':
    case 'Đang hoạt động tốt':
      return 'success'
    case 'Hiệu quả trung bình':
    case 'Cần cải thiện':
      return 'warning'
    case 'Hiệu quả thấp':
      return 'danger'
    case 'Sắp hết hạn':
      return 'info'
    default:
      return 'secondary'
  }
}

// Watch period changes
watch(selectedPeriod, () => {
  calculateAnalytics()
})

// Initialize
onMounted(async () => {
  await refreshAnalytics()
})
</script>

<style scoped>
.voucher-analytics {
  @apply space-y-6;
}

:deep(.p-chart) {
  @apply w-full;
}

:deep(.p-progressbar) {
  @apply rounded-full;
}

:deep(.p-progressbar .p-progressbar-value) {
  @apply rounded-full;
}
</style>
