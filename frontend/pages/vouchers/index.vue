<template>
  <div class="voucher-list-page">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Quản lý Voucher
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          Quản lý phiếu giảm giá và mã khuyến mãi
        </p>
      </div>
      <div class="flex gap-3">
        <Button
          icon="pi pi-refresh"
          label="Làm mới"
          severity="secondary"
          @click="refreshData"
          :loading="loading"
        />
        <Button
          icon="pi pi-download"
          label="Xuất Excel"
          severity="help"
          @click="exportVouchers"
        />
        <Button
          icon="pi pi-plus"
          label="Tạo Voucher"
          @click="navigateTo('/vouchers/create')"
        />
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <Card class="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ voucherStats.totalVouchers }}</div>
              <div class="text-blue-100">Tổng Voucher</div>
            </div>
            <i class="pi pi-ticket text-3xl text-blue-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-green-500 to-green-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ voucherStats.activeVouchers }}</div>
              <div class="text-green-100">Đang Hoạt Động</div>
            </div>
            <i class="pi pi-check-circle text-3xl text-green-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ voucherStats.totalUsage }}</div>
              <div class="text-orange-100">Lượt Sử Dụng</div>
            </div>
            <i class="pi pi-chart-line text-3xl text-orange-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ formatCurrency(voucherStats.totalDiscount) }}</div>
              <div class="text-purple-100">Tổng Giảm Giá</div>
            </div>
            <i class="pi pi-money-bill text-3xl text-purple-200"></i>
          </div>
        </template>
      </Card>
    </div>

    <!-- Filters -->
    <Card class="mb-6">
      <template #content>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium mb-2">Trạng thái</label>
            <Select
              v-model="filters.status"
              :options="statusOptions"
              option-label="label"
              option-value="value"
              placeholder="Chọn trạng thái"
              class="w-full"
            />
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">Loại giảm giá</label>
            <Select
              v-model="filters.type"
              :options="typeOptions"
              option-label="label"
              option-value="value"
              placeholder="Chọn loại"
              class="w-full"
            />
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">Đối tượng</label>
            <Select
              v-model="filters.customerType"
              :options="customerTypeOptions"
              option-label="label"
              option-value="value"
              placeholder="Chọn đối tượng"
              class="w-full"
            />
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">Khoảng thời gian</label>
            <DatePicker
              v-model="filters.dateRange"
              selection-mode="range"
              :manual-input="false"
              placeholder="Chọn khoảng thời gian"
              class="w-full"
            />
          </div>
        </div>

        <div class="flex justify-between items-center mt-4">
          <div class="flex gap-2">
            <Button
              label="Tất cả"
              severity="secondary"
              size="small"
              @click="applyFilterPreset('all')"
            />
            <Button
              label="Đang hoạt động"
              severity="success"
              size="small"
              @click="applyFilterPreset('active')"
            />
            <Button
              label="Đã hết hạn"
              severity="warning"
              size="small"
              @click="applyFilterPreset('expired')"
            />
          </div>
          <Button
            label="Xóa bộ lọc"
            severity="secondary"
            size="small"
            @click="clearFilters"
          />
        </div>
      </template>
    </Card>

    <!-- Voucher DataTable -->
    <Card>
      <template #content>
        <DataTable
          v-model:selection="selectedVouchers"
          :value="filteredVouchers"
          :loading="loading"
          paginator
          :rows="10"
          :rows-per-page-options="[5, 10, 20, 50]"
          data-key="id"
          selection-mode="multiple"
          :meta-key-selection="false"
          class="p-datatable-sm"
          current-page-report-template="Hiển thị {first} đến {last} trong tổng số {totalRecords} voucher"
          :global-filter-fields="['maPhieuGiamGia', 'moTa']"
        >
          <template #header>
            <div class="flex justify-between items-center">
              <div class="flex items-center gap-3">
                <IconField>
                  <InputIcon>
                    <i class="pi pi-search" />
                  </InputIcon>
                  <InputText
                    v-model="globalFilter"
                    placeholder="Tìm kiếm voucher..."
                    class="w-80"
                  />
                </IconField>
                <Button
                  v-if="hasSelectedVouchers"
                  icon="pi pi-trash"
                  label="Xóa đã chọn"
                  severity="danger"
                  size="small"
                  @click="confirmBulkDelete"
                  :loading="bulkOperationLoading"
                />
              </div>
              <div class="text-sm text-gray-600">
                {{ filteredVouchers.length }} voucher
              </div>
            </div>
          </template>

          <Column selection-mode="multiple" header-style="width: 3rem"></Column>

          <Column field="maPhieuGiamGia" header="Mã Voucher" sortable>
            <template #body="{ data }">
              <div class="font-mono font-semibold text-blue-600">
                {{ data.maPhieuGiamGia }}
              </div>
            </template>
          </Column>

          <Column field="loaiGiamGia" header="Loại" sortable>
            <template #body="{ data }">
              <Tag
                :value="voucherTypeMap[data.loaiGiamGia]?.label"
                :icon="voucherTypeMap[data.loaiGiamGia]?.icon"
                :severity="data.loaiGiamGia === 'PHAN_TRAM' ? 'info' : 'success'"
              />
            </template>
          </Column>

          <Column field="giaTriGiam" header="Giá trị" sortable>
            <template #body="{ data }">
              <div class="font-semibold">
                {{ formatDiscountValue(data) }}
              </div>
            </template>
          </Column>

          <Column field="trangThai" header="Trạng thái" sortable>
            <template #body="{ data }">
              <Tag
                :value="getVoucherStatusInfo(data.trangThai).label"
                :severity="getVoucherStatusInfo(data.trangThai).severity"
              />
            </template>
          </Column>

          <Column field="soLuongDaDung" header="Sử dụng" sortable>
            <template #body="{ data }">
              <div class="text-center">
                <div class="text-sm font-semibold">
                  {{ data.soLuongDaDung }} / {{ data.soLuongBanDau }}
                </div>
                <ProgressBar
                  :value="getVoucherUsageRate(data)"
                  class="mt-1"
                  style="height: 4px"
                />
              </div>
            </template>
          </Column>

          <Column field="ngayKetThuc" header="Hết hạn" sortable>
            <template #body="{ data }">
              <div class="text-sm">
                {{ formatDate(data.ngayKetThuc) }}
                <div class="text-xs text-gray-500">
                  {{ getVoucherRemainingDays(data) }} ngày
                </div>
              </div>
            </template>
          </Column>

          <Column header="Thao tác" style="width: 120px">
            <template #body="{ data }">
              <div class="flex gap-1">
                <Button
                  icon="pi pi-eye"
                  severity="info"
                  size="small"
                  @click="viewVoucher(data)"
                  v-tooltip="'Xem chi tiết'"
                />
                <Button
                  icon="pi pi-pencil"
                  severity="warning"
                  size="small"
                  @click="editVoucher(data)"
                  v-tooltip="'Chỉnh sửa'"
                />
                <Button
                  icon="pi pi-trash"
                  severity="danger"
                  size="small"
                  @click="confirmDelete(data)"
                  v-tooltip="'Xóa'"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </template>
    </Card>

    <!-- Delete Confirmation Dialog -->
    <ConfirmDialog />

    <!-- Bulk Delete Dialog -->
    <Dialog
      v-model:visible="showBulkDeleteDialog"
      header="Xác nhận xóa nhiều voucher"
      :modal="true"
      :closable="false"
      style="width: 450px"
    >
      <div class="flex items-center gap-3 mb-4">
        <i class="pi pi-exclamation-triangle text-orange-500 text-2xl"></i>
        <span>
          Bạn có chắc chắn muốn xóa {{ selectedVouchers.length }} voucher đã chọn?
        </span>
      </div>
      
      <div class="mb-4">
        <label class="block text-sm font-medium mb-2">Lý do xóa</label>
        <Textarea
          v-model="deleteReason"
          placeholder="Nhập lý do xóa (tùy chọn)"
          rows="3"
          class="w-full"
        />
      </div>

      <template #footer>
        <Button
          label="Hủy"
          severity="secondary"
          @click="showBulkDeleteDialog = false"
        />
        <Button
          label="Xóa"
          severity="danger"
          @click="executeBulkDelete"
          :loading="bulkOperationLoading"
        />
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth',
  layout: 'default'
})

// Store and composables
const voucherStore = useVoucherStore()
const { $confirm } = useNuxtApp()
const toast = useToast()

// Destructure store properties and methods
const {
  vouchers,
  filteredVouchers,
  voucherStats,
  loading,
  selectedVouchers,
  hasSelectedVouchers,
  bulkOperationLoading,
  voucherTypeMap,
  refreshVouchers,
  deleteVoucherWithRefresh,
  bulkDeleteVouchers,
  getVoucherUsageRate,
  getVoucherRemainingDays,
  getVoucherStatusInfo,
  formatDiscountValue,
  setFilters,
  clearFilters,
  applyFilterPreset,
  exportVouchers
} = voucherStore

// Local state
const globalFilter = ref('')
const showBulkDeleteDialog = ref(false)
const deleteReason = ref('')

// Filter options
const statusOptions = [
  { label: 'Tất cả', value: 'all' },
  { label: 'Chưa diễn ra', value: 'CHUA_DIEN_RA' },
  { label: 'Đang diễn ra', value: 'DA_DIEN_RA' },
  { label: 'Đã kết thúc', value: 'DA_KET_THUC' },
  { label: 'Đã hủy', value: 'DA_HUY' }
]

const typeOptions = [
  { label: 'Tất cả', value: 'all' },
  { label: 'Phần trăm', value: 'PHAN_TRAM' },
  { label: 'Số tiền cố định', value: 'SO_TIEN_CO_DINH' }
]

const customerTypeOptions = [
  { label: 'Tất cả', value: 'all' },
  { label: 'Công khai', value: 'public' },
  { label: 'Riêng tư', value: 'private' }
]

// Reactive filters
const filters = reactive({
  status: 'all',
  type: 'all',
  customerType: 'all',
  dateRange: null as [Date, Date] | null
})

// Watch filters and apply them to store
watch(filters, (newFilters) => {
  setFilters(newFilters)
}, { deep: true })

// Methods
const refreshData = async () => {
  await refreshVouchers(true)
}

const viewVoucher = (voucher: any) => {
  navigateTo(`/vouchers/${voucher.id}`)
}

const editVoucher = (voucher: any) => {
  navigateTo(`/vouchers/${voucher.id}/edit`)
}

const confirmDelete = (voucher: any) => {
  $confirm.require({
    message: `Bạn có chắc chắn muốn xóa voucher "${voucher.maPhieuGiamGia}"?`,
    header: 'Xác nhận xóa',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: 'Hủy',
      severity: 'secondary',
      outlined: true
    },
    acceptProps: {
      label: 'Xóa',
      severity: 'danger'
    },
    accept: async () => {
      const success = await deleteVoucherWithRefresh(voucher.id)
      if (success) {
        toast.add({
          severity: 'success',
          summary: 'Thành công',
          detail: 'Xóa voucher thành công',
          life: 3000
        })
      }
    }
  })
}

const confirmBulkDelete = () => {
  showBulkDeleteDialog.value = true
  deleteReason.value = ''
}

const executeBulkDelete = async () => {
  const ids = selectedVouchers.value.map(v => v.id).filter(Boolean) as number[]
  const results = await bulkDeleteVouchers(ids, deleteReason.value || undefined)

  const successCount = results.filter(r => r.success).length
  const failCount = results.length - successCount

  if (successCount > 0) {
    toast.add({
      severity: 'success',
      summary: 'Thành công',
      detail: `Đã xóa ${successCount} voucher`,
      life: 3000
    })
  }

  if (failCount > 0) {
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: `Không thể xóa ${failCount} voucher`,
      life: 5000
    })
  }

  showBulkDeleteDialog.value = false
}

// Initialize data
onMounted(async () => {
  await refreshData()
})

// SEO
useHead({
  title: 'Quản lý Voucher - LapXpert Admin',
  meta: [
    {
      name: 'description',
      content: 'Quản lý phiếu giảm giá và mã khuyến mãi cho hệ thống LapXpert'
    }
  ]
})
</script>

<style scoped>
.voucher-list-page {
  @apply p-6;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  @apply py-3;
}

:deep(.p-progressbar) {
  @apply rounded-full;
}

:deep(.p-progressbar .p-progressbar-value) {
  @apply rounded-full;
}
</style>
