<template>
  <div class="voucher-edit-page">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Chỉnh sửa Voucher
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          Cập nhật thông tin voucher: {{ currentVoucher?.maPhieuGiamGia }}
        </p>
      </div>
      <div class="flex gap-3">
        <Button
          icon="pi pi-arrow-left"
          label="Quay lại"
          severity="secondary"
          @click="navigateTo('/vouchers')"
        />
        <Button
          icon="pi pi-eye"
          label="Xem chi tiết"
          severity="info"
          @click="navigateTo(`/vouchers/${route.params.id}`)"
        />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center h-64">
      <ProgressSpinner />
    </div>

    <!-- Error State -->
    <Message v-else-if="error" severity="error" :closable="false">
      {{ error }}
    </Message>

    <!-- Edit Form -->
    <VoucherForm
      v-else-if="currentVoucher"
      :voucher="currentVoucher"
      :is-edit="true"
      @submit="handleUpdate"
      @cancel="navigateTo('/vouchers')"
    />
  </div>
</template>

<script setup lang="ts">
import type { Voucher } from '~/composables/useVoucher'

definePageMeta({
  middleware: 'auth',
  layout: 'default'
})

// Route and store
const route = useRoute()
const voucherStore = useVoucherStore()
const toast = useToast()

// Destructure store
const {
  currentVoucher,
  loading,
  error,
  fetchVoucherById,
  updateVoucherWithRefresh
} = voucherStore

// Methods
const handleUpdate = async (voucherData: Partial<Voucher>) => {
  try {
    const result = await updateVoucherWithRefresh(Number(route.params.id), voucherData)
    if (result) {
      toast.add({
        severity: 'success',
        summary: 'Thành công',
        detail: 'Cập nhật voucher thành công',
        life: 3000
      })
      navigateTo('/vouchers')
    }
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Không thể cập nhật voucher',
      life: 5000
    })
  }
}

// Initialize
onMounted(async () => {
  const voucherId = Number(route.params.id)
  if (voucherId) {
    await fetchVoucherById(voucherId)
  } else {
    navigateTo('/vouchers')
  }
})

// SEO
useHead({
  title: computed(() => `Chỉnh sửa Voucher ${currentVoucher.value?.maPhieuGiamGia || ''} - LapXpert Admin`),
  meta: [
    {
      name: 'description',
      content: 'Chỉnh sửa thông tin voucher'
    }
  ]
})
</script>

<style scoped>
.voucher-edit-page {
  @apply p-6;
}
</style>
