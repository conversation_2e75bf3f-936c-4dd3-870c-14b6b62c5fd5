<template>
  <div class="voucher-detail-page">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Chi tiết Voucher
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          Thông tin chi tiết voucher: {{ currentVoucher?.maPhieuGiamGia }}
        </p>
      </div>
      <div class="flex gap-3">
        <Button
          icon="pi pi-arrow-left"
          label="Quay lại"
          severity="secondary"
          @click="navigateTo('/vouchers')"
        />
        <Button
          v-if="currentVoucher"
          icon="pi pi-pencil"
          label="Chỉnh sửa"
          severity="warning"
          @click="navigateTo(`/vouchers/${route.params.id}/edit`)"
        />
        <Button
          v-if="currentVoucher"
          icon="pi pi-trash"
          label="Xóa"
          severity="danger"
          @click="confirmDelete"
        />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center h-64">
      <ProgressSpinner />
    </div>

    <!-- Error State -->
    <Message v-else-if="error" severity="error" :closable="false">
      {{ error }}
    </Message>

    <!-- Voucher Details -->
    <div v-else-if="currentVoucher" class="space-y-6">
      <!-- Voucher Card Preview -->
      <Card>
        <template #content>
          <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-8 rounded-lg">
            <div class="flex justify-between items-start">
              <div>
                <div class="text-3xl font-bold mb-3">{{ currentVoucher.maPhieuGiamGia }}</div>
                <div class="text-xl mb-2">
                  Giảm {{ formatDiscountValue(currentVoucher) }}
                </div>
                <div v-if="currentVoucher.giaTriDonHangToiThieu" class="text-sm opacity-90 mb-2">
                  Đơn hàng từ {{ formatCurrency(currentVoucher.giaTriDonHangToiThieu) }}
                </div>
                <div class="text-sm opacity-90">
                  Có hiệu lực: {{ formatDate(currentVoucher.ngayBatDau) }} - {{ formatDate(currentVoucher.ngayKetThuc) }}
                </div>
              </div>
              <div class="text-right">
                <Tag
                  :value="getVoucherStatusInfo(currentVoucher.trangThai).label"
                  :severity="getVoucherStatusInfo(currentVoucher.trangThai).severity"
                  class="mb-2"
                />
                <div class="text-sm opacity-90">Số lượng</div>
                <div class="text-2xl font-bold">
                  {{ currentVoucher.soLuongDaDung }} / {{ currentVoucher.soLuongBanDau }}
                </div>
                <ProgressBar
                  :value="getVoucherUsageRate(currentVoucher)"
                  class="mt-2 w-24"
                  style="height: 6px"
                />
              </div>
            </div>
            <div v-if="currentVoucher.moTa" class="mt-4 text-sm opacity-90">
              {{ currentVoucher.moTa }}
            </div>
          </div>
        </template>
      </Card>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <template #content>
            <div class="text-center">
              <div class="text-3xl font-bold text-blue-600 mb-2">
                {{ getVoucherUsageRate(currentVoucher).toFixed(1) }}%
              </div>
              <div class="text-gray-600">Tỷ lệ sử dụng</div>
            </div>
          </template>
        </Card>

        <Card>
          <template #content>
            <div class="text-center">
              <div class="text-3xl font-bold text-green-600 mb-2">
                {{ getVoucherRemainingDays(currentVoucher) }}
              </div>
              <div class="text-gray-600">Ngày còn lại</div>
            </div>
          </template>
        </Card>

        <Card>
          <template #content>
            <div class="text-center">
              <div class="text-3xl font-bold text-purple-600 mb-2">
                {{ formatCurrency(estimatedTotalDiscount) }}
              </div>
              <div class="text-gray-600">Tổng giảm giá ước tính</div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Detailed Information -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <Card>
          <template #header>
            <div class="p-4 border-b">
              <h3 class="text-lg font-semibold">Thông tin cơ bản</h3>
            </div>
          </template>
          <template #content>
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="font-medium">Mã voucher:</span>
                <span class="font-mono">{{ currentVoucher.maPhieuGiamGia }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Loại giảm giá:</span>
                <Tag
                  :value="voucherTypeMap[currentVoucher.loaiGiamGia]?.label"
                  :icon="voucherTypeMap[currentVoucher.loaiGiamGia]?.icon"
                  :severity="currentVoucher.loaiGiamGia === 'PHAN_TRAM' ? 'info' : 'success'"
                />
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Giá trị giảm:</span>
                <span class="font-semibold">{{ formatDiscountValue(currentVoucher) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Đơn hàng tối thiểu:</span>
                <span>
                  {{ currentVoucher.giaTriDonHangToiThieu ? formatCurrency(currentVoucher.giaTriDonHangToiThieu) : 'Không giới hạn' }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Trạng thái:</span>
                <Tag
                  :value="getVoucherStatusInfo(currentVoucher.trangThai).label"
                  :severity="getVoucherStatusInfo(currentVoucher.trangThai).severity"
                />
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Hiệu quả:</span>
                <span class="font-semibold">{{ getVoucherEffectiveness(currentVoucher) }}</span>
              </div>
            </div>
          </template>
        </Card>

        <!-- Usage Information -->
        <Card>
          <template #header>
            <div class="p-4 border-b">
              <h3 class="text-lg font-semibold">Thông tin sử dụng</h3>
            </div>
          </template>
          <template #content>
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="font-medium">Số lượng ban đầu:</span>
                <span>{{ currentVoucher.soLuongBanDau }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Đã sử dụng:</span>
                <span class="font-semibold text-blue-600">{{ currentVoucher.soLuongDaDung }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Còn lại:</span>
                <span class="font-semibold text-green-600">
                  {{ currentVoucher.soLuongBanDau - currentVoucher.soLuongDaDung }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Tỷ lệ sử dụng:</span>
                <div class="flex items-center gap-2">
                  <ProgressBar
                    :value="getVoucherUsageRate(currentVoucher)"
                    class="w-20"
                    style="height: 8px"
                  />
                  <span class="text-sm font-semibold">
                    {{ getVoucherUsageRate(currentVoucher).toFixed(1) }}%
                  </span>
                </div>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Ngày bắt đầu:</span>
                <span>{{ formatDateTime(currentVoucher.ngayBatDau) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Ngày kết thúc:</span>
                <span>{{ formatDateTime(currentVoucher.ngayKetThuc) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium">Ngày còn lại:</span>
                <span class="font-semibold">
                  {{ getVoucherRemainingDays(currentVoucher) }} ngày
                </span>
              </div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Customer Assignment -->
      <Card v-if="currentVoucher.danhSachNguoiDung && currentVoucher.danhSachNguoiDung.length > 0">
        <template #header>
          <div class="p-4 border-b">
            <h3 class="text-lg font-semibold">Khách hàng được phân quyền</h3>
          </div>
        </template>
        <template #content>
          <div class="text-center py-8">
            <i class="pi pi-users text-4xl text-gray-400 mb-4"></i>
            <div class="text-lg font-semibold mb-2">
              {{ currentVoucher.danhSachNguoiDung.length }} khách hàng
            </div>
            <div class="text-gray-600">
              Voucher này chỉ dành cho khách hàng được chỉ định
            </div>
          </div>
        </template>
      </Card>

      <Card v-else>
        <template #header>
          <div class="p-4 border-b">
            <h3 class="text-lg font-semibold">Phạm vi sử dụng</h3>
          </div>
        </template>
        <template #content>
          <div class="text-center py-8">
            <i class="pi pi-globe text-4xl text-green-400 mb-4"></i>
            <div class="text-lg font-semibold mb-2">Voucher công khai</div>
            <div class="text-gray-600">
              Tất cả khách hàng đều có thể sử dụng voucher này
            </div>
          </div>
        </template>
      </Card>

      <!-- Description -->
      <Card v-if="currentVoucher.moTa">
        <template #header>
          <div class="p-4 border-b">
            <h3 class="text-lg font-semibold">Mô tả</h3>
          </div>
        </template>
        <template #content>
          <p class="text-gray-700 leading-relaxed">
            {{ currentVoucher.moTa }}
          </p>
        </template>
      </Card>

      <!-- Audit Information -->
      <Card>
        <template #header>
          <div class="p-4 border-b">
            <h3 class="text-lg font-semibold">Thông tin audit</h3>
          </div>
        </template>
        <template #content>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex justify-between">
              <span class="font-medium">Ngày tạo:</span>
              <span>{{ formatDateTime(currentVoucher.ngayTao) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">Ngày cập nhật:</span>
              <span>{{ formatDateTime(currentVoucher.ngayCapNhat) }}</span>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Delete Confirmation Dialog -->
    <ConfirmDialog />
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth',
  layout: 'default'
})

// Route and store
const route = useRoute()
const voucherStore = useVoucherStore()
const { $confirm } = useNuxtApp()
const toast = useToast()

// Destructure store
const {
  currentVoucher,
  loading,
  error,
  voucherTypeMap,
  fetchVoucherById,
  deleteVoucherWithRefresh,
  getVoucherUsageRate,
  getVoucherRemainingDays,
  getVoucherStatusInfo,
  getVoucherEffectiveness,
  formatDiscountValue
} = voucherStore

// Computed properties
const estimatedTotalDiscount = computed(() => {
  if (!currentVoucher.value) return 0

  // Estimate total discount based on usage and average discount value
  const usageCount = currentVoucher.value.soLuongDaDung
  const discountValue = currentVoucher.value.giaTriGiam

  if (currentVoucher.value.loaiGiamGia === 'PHAN_TRAM') {
    // For percentage discounts, estimate based on average order value
    const estimatedAverageOrder = 20000000 // 20M VND average
    return (usageCount * estimatedAverageOrder * discountValue) / 100
  } else {
    // For fixed amount discounts
    return usageCount * discountValue
  }
})

// Methods
const confirmDelete = () => {
  if (!currentVoucher.value) return

  $confirm.require({
    message: `Bạn có chắc chắn muốn xóa voucher "${currentVoucher.value.maPhieuGiamGia}"?`,
    header: 'Xác nhận xóa',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: 'Hủy',
      severity: 'secondary',
      outlined: true
    },
    acceptProps: {
      label: 'Xóa',
      severity: 'danger'
    },
    accept: async () => {
      const success = await deleteVoucherWithRefresh(Number(route.params.id))
      if (success) {
        toast.add({
          severity: 'success',
          summary: 'Thành công',
          detail: 'Xóa voucher thành công',
          life: 3000
        })
        navigateTo('/vouchers')
      }
    }
  })
}

// Initialize
onMounted(async () => {
  const voucherId = Number(route.params.id)
  if (voucherId) {
    await fetchVoucherById(voucherId)

    if (!currentVoucher.value) {
      toast.add({
        severity: 'error',
        summary: 'Lỗi',
        detail: 'Không tìm thấy voucher',
        life: 5000
      })
      navigateTo('/vouchers')
    }
  } else {
    navigateTo('/vouchers')
  }
})

// SEO
useHead({
  title: computed(() => `Chi tiết Voucher ${currentVoucher.value?.maPhieuGiamGia || ''} - LapXpert Admin`),
  meta: [
    {
      name: 'description',
      content: computed(() => `Chi tiết voucher ${currentVoucher.value?.maPhieuGiamGia || ''}`)
    }
  ]
})
</script>

<style scoped>
.voucher-detail-page {
  @apply p-6;
}

:deep(.p-progressbar) {
  @apply rounded-full;
}

:deep(.p-progressbar .p-progressbar-value) {
  @apply rounded-full;
}

:deep(.p-card .p-card-content) {
  @apply p-4;
}
</style>
