<template>
  <div class="voucher-analytics-page">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Phân tích Voucher
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          Thống kê chi tiết và phân tích hiệu suất voucher
        </p>
      </div>
      <div class="flex gap-3">
        <Button
          icon="pi pi-arrow-left"
          label="Quay lại"
          severity="secondary"
          @click="navigateTo('/vouchers')"
        />
        <Button
          icon="pi pi-download"
          label="Xuất báo cáo"
          severity="help"
          @click="exportReport"
        />
      </div>
    </div>

    <!-- Analytics Component -->
    <VoucherAnalytics />

    <!-- Additional Insights -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
      <!-- Recommendations -->
      <Card>
        <template #header>
          <div class="p-4 border-b">
            <h3 class="text-lg font-semibold flex items-center gap-2">
              <i class="pi pi-lightbulb text-yellow-500"></i>
              Đề xuất cải thiện
            </h3>
          </div>
        </template>
        <template #content>
          <div class="space-y-4">
            <div v-for="recommendation in recommendations" :key="recommendation.id" 
                 class="p-4 border rounded-lg">
              <div class="flex items-start gap-3">
                <i :class="recommendation.icon" :style="{ color: recommendation.color }"></i>
                <div>
                  <h4 class="font-semibold mb-1">{{ recommendation.title }}</h4>
                  <p class="text-sm text-gray-600 mb-2">{{ recommendation.description }}</p>
                  <Tag
                    :value="recommendation.priority"
                    :severity="getPrioritySeverity(recommendation.priority)"
                    size="small"
                  />
                </div>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- Trends & Insights -->
      <Card>
        <template #header>
          <div class="p-4 border-b">
            <h3 class="text-lg font-semibold flex items-center gap-2">
              <i class="pi pi-chart-line text-blue-500"></i>
              Xu hướng & Nhận định
            </h3>
          </div>
        </template>
        <template #content>
          <div class="space-y-4">
            <div v-for="insight in insights" :key="insight.id" 
                 class="p-4 border rounded-lg">
              <div class="flex items-start gap-3">
                <div class="flex-shrink-0">
                  <div :class="insight.trendClass" class="w-8 h-8 rounded-full flex items-center justify-center">
                    <i :class="insight.trendIcon" class="text-sm"></i>
                  </div>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">{{ insight.title }}</h4>
                  <p class="text-sm text-gray-600 mb-2">{{ insight.description }}</p>
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-medium" :class="insight.valueClass">
                      {{ insight.value }}
                    </span>
                    <span class="text-xs text-gray-500">{{ insight.period }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Detailed Performance Table -->
    <Card class="mt-8">
      <template #header>
        <div class="p-4 border-b">
          <h3 class="text-lg font-semibold">Hiệu suất chi tiết theo voucher</h3>
        </div>
      </template>
      <template #content>
        <DataTable
          :value="detailedPerformance"
          :loading="loading"
          paginator
          :rows="10"
          :rows-per-page-options="[5, 10, 20]"
          class="p-datatable-sm"
          sortable
        >
          <Column field="maPhieuGiamGia" header="Mã Voucher" sortable>
            <template #body="{ data }">
              <NuxtLink 
                :to="`/vouchers/${data.id}`"
                class="font-mono font-semibold text-blue-600 hover:text-blue-800"
              >
                {{ data.maPhieuGiamGia }}
              </NuxtLink>
            </template>
          </Column>

          <Column field="loaiGiamGia" header="Loại" sortable>
            <template #body="{ data }">
              <Tag
                :value="data.loaiGiamGia === 'PHAN_TRAM' ? 'Phần trăm' : 'Số tiền'"
                :severity="data.loaiGiamGia === 'PHAN_TRAM' ? 'info' : 'success'"
              />
            </template>
          </Column>

          <Column field="usageRate" header="Tỷ lệ sử dụng" sortable>
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <ProgressBar
                  :value="data.usageRate"
                  class="w-20"
                  style="height: 6px"
                />
                <span class="text-sm font-semibold">{{ data.usageRate.toFixed(1) }}%</span>
              </div>
            </template>
          </Column>

          <Column field="totalRevenue" header="Doanh thu tạo ra" sortable>
            <template #body="{ data }">
              <div class="font-semibold text-green-600">
                {{ formatCurrency(data.totalRevenue) }}
              </div>
            </template>
          </Column>

          <Column field="roi" header="ROI" sortable>
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span class="font-semibold" :class="data.roi >= 0 ? 'text-green-600' : 'text-red-600'">
                  {{ data.roi.toFixed(1) }}%
                </span>
                <i :class="data.roi >= 0 ? 'pi pi-arrow-up text-green-500' : 'pi pi-arrow-down text-red-500'"></i>
              </div>
            </template>
          </Column>

          <Column field="customerSatisfaction" header="Hài lòng KH" sortable>
            <template #body="{ data }">
              <Rating 
                :model-value="data.customerSatisfaction" 
                readonly 
                :cancel="false"
                class="text-sm"
              />
            </template>
          </Column>

          <Column field="effectiveness" header="Hiệu quả tổng thể" sortable>
            <template #body="{ data }">
              <Tag
                :value="data.effectiveness"
                :severity="getEffectivenessSeverity(data.effectiveness)"
              />
            </template>
          </Column>

          <Column header="Thao tác" style="width: 100px">
            <template #body="{ data }">
              <div class="flex gap-1">
                <Button
                  icon="pi pi-eye"
                  severity="info"
                  size="small"
                  @click="navigateTo(`/vouchers/${data.id}`)"
                  v-tooltip="'Xem chi tiết'"
                />
                <Button
                  icon="pi pi-chart-line"
                  severity="success"
                  size="small"
                  @click="viewDetailedAnalytics(data)"
                  v-tooltip="'Phân tích chi tiết'"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth',
  layout: 'default'
})

// Store and composables
const voucherStore = useVoucherStore()
const toast = useToast()

// Destructure store
const {
  vouchers,
  loading,
  getVoucherUsageRate,
  getVoucherEffectiveness,
  refreshVouchers
} = voucherStore

// Computed data
const detailedPerformance = computed(() => {
  return vouchers.value.map(voucher => ({
    ...voucher,
    usageRate: getVoucherUsageRate(voucher),
    totalRevenue: voucher.soLuongDaDung * 20000000, // Mock average order value
    roi: (Math.random() - 0.3) * 200, // Mock ROI calculation
    customerSatisfaction: Math.floor(Math.random() * 2) + 4, // 4-5 stars
    effectiveness: getVoucherEffectiveness(voucher)
  }))
})

const recommendations = ref([
  {
    id: 1,
    title: 'Tối ưu voucher phần trăm',
    description: 'Voucher phần trăm có tỷ lệ sử dụng cao hơn 23% so với voucher số tiền cố định',
    priority: 'Cao',
    icon: 'pi pi-percentage',
    color: '#10B981'
  },
  {
    id: 2,
    title: 'Điều chỉnh thời gian hiệu lực',
    description: 'Voucher có thời hạn 30 ngày có hiệu quả tốt nhất',
    priority: 'Trung bình',
    icon: 'pi pi-calendar',
    color: '#F59E0B'
  },
  {
    id: 3,
    title: 'Tăng cường marketing',
    description: '15% voucher có tỷ lệ sử dụng dưới 30%, cần tăng cường quảng bá',
    priority: 'Thấp',
    icon: 'pi pi-megaphone',
    color: '#6B7280'
  }
])

const insights = ref([
  {
    id: 1,
    title: 'Tăng trưởng sử dụng voucher',
    description: 'Lượt sử dụng voucher tăng mạnh trong tháng qua',
    value: '+24.5%',
    period: 'so với tháng trước',
    trendClass: 'bg-green-100 text-green-600',
    trendIcon: 'pi pi-arrow-up',
    valueClass: 'text-green-600'
  },
  {
    id: 2,
    title: 'Hiệu quả voucher cuối tuần',
    description: 'Voucher được sử dụng nhiều nhất vào cuối tuần',
    value: '68%',
    period: 'tổng lượt sử dụng',
    trendClass: 'bg-blue-100 text-blue-600',
    trendIcon: 'pi pi-chart-bar',
    valueClass: 'text-blue-600'
  },
  {
    id: 3,
    title: 'Giá trị đơn hàng trung bình',
    description: 'Đơn hàng có voucher có giá trị cao hơn đơn hàng thường',
    value: '+18.2%',
    period: 'so với đơn hàng thường',
    trendClass: 'bg-purple-100 text-purple-600',
    trendIcon: 'pi pi-arrow-up',
    valueClass: 'text-purple-600'
  }
])

// Methods
const exportReport = () => {
  // Mock export functionality
  toast.add({
    severity: 'success',
    summary: 'Thành công',
    detail: 'Báo cáo đã được xuất thành công',
    life: 3000
  })
}

const getPrioritySeverity = (priority: string) => {
  switch (priority) {
    case 'Cao': return 'danger'
    case 'Trung bình': return 'warning'
    case 'Thấp': return 'info'
    default: return 'secondary'
  }
}

const getEffectivenessSeverity = (effectiveness: string) => {
  switch (effectiveness) {
    case 'Hiệu quả cao':
    case 'Đang hoạt động tốt':
      return 'success'
    case 'Hiệu quả trung bình':
    case 'Cần cải thiện':
      return 'warning'
    case 'Hiệu quả thấp':
      return 'danger'
    case 'Sắp hết hạn':
      return 'info'
    default:
      return 'secondary'
  }
}

const viewDetailedAnalytics = (voucher: any) => {
  // Navigate to detailed analytics for specific voucher
  navigateTo(`/vouchers/${voucher.id}/analytics`)
}

// Initialize
onMounted(async () => {
  await refreshVouchers()
})

// SEO
useHead({
  title: 'Phân tích Voucher - LapXpert Admin',
  meta: [
    {
      name: 'description',
      content: 'Phân tích chi tiết hiệu suất và xu hướng sử dụng voucher'
    }
  ]
})
</script>

<style scoped>
.voucher-analytics-page {
  @apply p-6;
}

:deep(.p-progressbar) {
  @apply rounded-full;
}

:deep(.p-progressbar .p-progressbar-value) {
  @apply rounded-full;
}

:deep(.p-rating .p-rating-item) {
  @apply text-xs;
}
</style>
