<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Chi tiết đơn hàng
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Xem và quản lý thông tin đơn hàng
        </p>
      </div>
      <div class="flex gap-3">
        <Button
          icon="pi pi-arrow-left"
          label="Quay lại"
          outlined
          @click="$router.back()"
        />
        <Button
          v-if="order && canEditOrder"
          icon="pi pi-pencil"
          label="Chỉnh sửa"
          @click="editOrder"
        />
        <Button
          v-if="order && canCancelOrder"
          icon="pi pi-times"
          label="Hủy đơn hàng"
          severity="danger"
          outlined
          @click="showCancelDialog = true"
        />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="card">
      <div class="flex items-center justify-center py-12">
        <ProgressSpinner />
      </div>
    </div>

    <!-- Order Details -->
    <div v-else-if="order" class="space-y-6">
      <!-- Order Summary -->
      <div class="card">
        <div class="card-header">
          <div class="flex justify-between items-center">
            <h3 class="card-title">Thông tin đơn hàng</h3>
            <div class="flex gap-2">
              <Tag
                :value="getOrderStatusText(order.trangThaiDonHang)"
                :severity="getOrderStatusSeverity(order.trangThaiDonHang)"
                class="text-sm"
              />
              <Tag
                :value="getPaymentStatusText(order.trangThaiThanhToan)"
                :severity="getPaymentStatusSeverity(order.trangThaiThanhToan)"
                class="text-sm"
              />
            </div>
          </div>
        </div>
        <div class="card-content">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Mã đơn hàng
              </label>
              <p class="font-mono text-sm bg-gray-100 dark:bg-gray-700 p-2 rounded">
                {{ order.maHoaDon }}
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Loại đơn hàng
              </label>
              <Tag :value="getOrderTypeText(order.loaiHoaDon)" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Ngày tạo
              </label>
              <p>{{ formatDateTime(order.ngayTao) }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tổng tiền
              </label>
              <p class="text-lg font-semibold text-green-600">{{ formatCurrency(order.tongThanhToan) }}</p>
            </div>

            <div v-if="order.maVanDon">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Mã vận đơn
              </label>
              <p class="font-mono text-sm">{{ order.maVanDon }}</p>
            </div>

            <div v-if="order.ngayDuKienGiaoHang">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Ngày dự kiến giao hàng
              </label>
              <p>{{ formatDate(order.ngayDuKienGiaoHang) }}</p>
            </div>
          </div>

          <div v-if="order.ghiChu" class="mt-6">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Ghi chú
            </label>
            <p class="text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded">
              {{ order.ghiChu }}
            </p>
          </div>
        </div>
      </div>

      <!-- Customer Information -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Thông tin khách hàng</h3>
        </div>
        <div class="card-content">
          <div v-if="order.khachHang" class="flex items-center gap-4 mb-6">
            <Avatar
              v-if="order.khachHang.avatar"
              :image="order.khachHang.avatar"
              size="large"
              shape="circle"
            />
            <Avatar
              v-else
              :label="order.khachHang.hoTen?.charAt(0) || 'U'"
              size="large"
              shape="circle"
              style="background-color: #dee2e6; color: #495057"
            />
            <div>
              <h4 class="text-lg font-semibold">{{ order.khachHang.hoTen || 'Khách vãng lai' }}</h4>
              <p class="text-gray-600 dark:text-gray-400">{{ order.khachHang.email }}</p>
              <p class="text-gray-600 dark:text-gray-400">{{ order.khachHang.soDienThoai }}</p>
            </div>
          </div>

          <!-- Delivery Address -->
          <div v-if="order.diaChiGiaoHang">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Địa chỉ giao hàng
            </label>
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded">
              <p class="font-medium">{{ order.diaChiGiaoHang.hoTen }}</p>
              <p class="text-gray-600 dark:text-gray-400">{{ order.diaChiGiaoHang.soDienThoai }}</p>
              <p class="text-gray-600 dark:text-gray-400">
                {{ order.diaChiGiaoHang.diaChiChiTiet }}, 
                {{ order.diaChiGiaoHang.phuongXa }}, 
                {{ order.diaChiGiaoHang.quanHuyen }}, 
                {{ order.diaChiGiaoHang.tinhThanhPho }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Items -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Sản phẩm đặt hàng</h3>
        </div>
        <div class="card-content">
          <DataTable
            :value="order.hoaDonChiTiets"
            responsiveLayout="scroll"
            class="p-datatable-sm"
          >
            <Column field="sanPhamChiTiet" header="Sản phẩm" style="min-width: 300px">
              <template #body="{ data }">
                <div class="flex items-center gap-3">
                  <img
                    v-if="data.sanPhamChiTiet?.hinhAnh"
                    :src="data.sanPhamChiTiet.hinhAnh"
                    :alt="data.sanPhamChiTiet.sanPham?.tenSanPham"
                    class="w-12 h-12 rounded-lg object-cover"
                  />
                  <div v-else class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <i class="pi pi-image text-gray-400"></i>
                  </div>
                  <div>
                    <div class="font-medium">{{ data.sanPhamChiTiet?.sanPham?.tenSanPham }}</div>
                    <div class="text-sm text-gray-500">SKU: {{ data.sanPhamChiTiet?.sku }}</div>
                  </div>
                </div>
              </template>
            </Column>

            <Column field="donGia" header="Đơn giá" style="min-width: 120px">
              <template #body="{ data }">
                <span class="font-medium">{{ formatCurrency(data.donGia) }}</span>
              </template>
            </Column>

            <Column field="soLuong" header="Số lượng" style="min-width: 100px">
              <template #body="{ data }">
                <Badge :value="data.soLuong" severity="info" />
              </template>
            </Column>

            <Column field="thanhTien" header="Thành tiền" style="min-width: 120px">
              <template #body="{ data }">
                <span class="font-medium">{{ formatCurrency(data.thanhTien) }}</span>
              </template>
            </Column>

            <template #empty>
              <div class="text-center py-4">
                <p class="text-gray-500">Không có sản phẩm nào</p>
              </div>
            </template>
          </DataTable>

          <!-- Order Summary -->
          <div class="mt-6 border-t pt-6">
            <div class="flex justify-end">
              <div class="w-full max-w-sm space-y-2">
                <div class="flex justify-between">
                  <span>Tổng tiền hàng:</span>
                  <span class="font-medium">{{ formatCurrency(order.tongTien) }}</span>
                </div>
                <div v-if="order.tongGiamGia > 0" class="flex justify-between text-red-600">
                  <span>Giảm giá:</span>
                  <span class="font-medium">-{{ formatCurrency(order.tongGiamGia) }}</span>
                </div>
                <div v-if="order.phiVanChuyen > 0" class="flex justify-between">
                  <span>Phí vận chuyển:</span>
                  <span class="font-medium">{{ formatCurrency(order.phiVanChuyen) }}</span>
                </div>
                <div class="flex justify-between text-lg font-bold border-t pt-2">
                  <span>Tổng thanh toán:</span>
                  <span class="text-green-600">{{ formatCurrency(order.tongThanhToan) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Staff Information -->
      <div v-if="order.nhanVien" class="card">
        <div class="card-header">
          <h3 class="card-title">Thông tin nhân viên xử lý</h3>
        </div>
        <div class="card-content">
          <div class="flex items-center gap-4">
            <Avatar
              :label="order.nhanVien.hoTen?.charAt(0) || 'S'"
              size="normal"
              shape="circle"
              style="background-color: #3B82F6; color: white"
            />
            <div>
              <p class="font-medium">{{ order.nhanVien.hoTen }}</p>
              <p class="text-sm text-gray-500">{{ order.nhanVien.email }}</p>
              <p class="text-sm text-gray-500">{{ order.nhanVien.chucVu || 'Nhân viên' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Status Management -->
      <OrderStatusManager
        v-if="order.id"
        :orderId="order.id"
        :currentStatus="order.trangThaiDonHang"
        :currentPaymentStatus="order.trangThaiThanhToan"
        :canUpdateStatus="canEditOrder"
        @statusUpdated="handleStatusUpdated"
        @paymentConfirmed="handlePaymentConfirmed"
      />

      <!-- Order Timeline -->
      <OrderTimeline
        v-if="order.id"
        :orderId="order.id"
        ref="timelineRef"
      />
    </div>

    <!-- Error State -->
    <div v-else class="card">
      <div class="text-center py-12">
        <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-4"></i>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Không tìm thấy đơn hàng
        </h3>
        <p class="text-gray-600 dark:text-gray-400">
          Đơn hàng có thể đã bị xóa hoặc không tồn tại.
        </p>
      </div>
    </div>

    <!-- Cancel Order Dialog -->
    <Dialog v-model:visible="showCancelDialog" modal header="Hủy đơn hàng" :style="{ width: '500px' }">
      <div class="space-y-4">
        <p>Bạn có chắc chắn muốn hủy đơn hàng <strong>{{ order?.maHoaDon }}</strong>?</p>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Lý do hủy
          </label>
          <Textarea
            v-model="cancelReason"
            placeholder="Nhập lý do hủy đơn hàng"
            rows="3"
            class="w-full"
          />
        </div>
      </div>

      <template #footer>
        <Button
          label="Không"
          outlined
          @click="showCancelDialog = false"
        />
        <Button
          label="Hủy đơn hàng"
          severity="danger"
          @click="cancelOrderConfirmed"
          :loading="cancelling"
        />
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import type { Order } from '~/composables/useOrder'

definePageMeta({
  middleware: 'auth'
})

const route = useRoute()
const router = useRouter()
const { 
  getOrderById, 
  cancelOrder,
  getOrderStatusText,
  getPaymentStatusText,
  getOrderTypeText,
  getOrderStatusSeverity,
  getPaymentStatusSeverity
} = useOrder()

// Reactive data
const loading = ref(true)
const order = ref<Order | null>(null)
const showCancelDialog = ref(false)
const cancelReason = ref('')
const cancelling = ref(false)

// Component refs
const timelineRef = ref()

/**
 * Load order data
 */
const loadOrder = async () => {
  const orderId = parseInt(route.params.id as string)
  
  if (isNaN(orderId)) {
    router.push('/orders')
    return
  }

  loading.value = true
  try {
    order.value = await getOrderById(orderId)
  } catch (error) {
    console.error('Error loading order:', error)
  } finally {
    loading.value = false
  }
}

/**
 * Check if order can be edited
 */
const canEditOrder = computed(() => {
  if (!order.value) return false
  return !['DA_GIAO_HANG', 'DA_HUY'].includes(order.value.trangThaiDonHang!)
})

/**
 * Check if order can be cancelled
 */
const canCancelOrder = computed(() => {
  if (!order.value) return false
  return !['DA_GIAO_HANG', 'DA_HUY'].includes(order.value.trangThaiDonHang!)
})

/**
 * Edit order
 */
const editOrder = () => {
  router.push(`/orders/${route.params.id}/edit`)
}

/**
 * Cancel order confirmed
 */
const cancelOrderConfirmed = async () => {
  if (!order.value) return

  cancelling.value = true
  try {
    await cancelOrder(order.value.id!, cancelReason.value)
    await loadOrder() // Reload order data
    showCancelDialog.value = false

    // Refresh timeline
    if (timelineRef.value) {
      timelineRef.value.refresh()
    }
  } catch (error) {
    // Error is handled by the composable
  } finally {
    cancelling.value = false
  }
}

/**
 * Handle status updated
 */
const handleStatusUpdated = async (newStatus: string) => {
  await loadOrder() // Reload order data

  // Refresh timeline
  if (timelineRef.value) {
    timelineRef.value.refresh()
  }
}

/**
 * Handle payment confirmed
 */
const handlePaymentConfirmed = async (paymentMethod: string) => {
  await loadOrder() // Reload order data

  // Refresh timeline
  if (timelineRef.value) {
    timelineRef.value.refresh()
  }
}

// Load order on mount
onMounted(() => {
  loadOrder()
})

// Set page title
useHead({
  title: computed(() => order.value ? `Đơn hàng ${order.value.maHoaDon} - LapXpert Admin` : 'Chi tiết đơn hàng - LapXpert Admin')
})
</script>
