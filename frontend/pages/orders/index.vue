<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Quản lý đơn hàng
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Theo dõi và xử lý đơn hàng của khách hàng
        </p>
      </div>
      <div class="flex gap-3">
        <Button
          icon="pi pi-refresh"
          label="Làm mới"
          outlined
          @click="refreshData"
          :loading="loading"
        />
        <Button
          icon="pi pi-plus"
          label="Tạo đơn hàng"
          @click="navigateTo('/orders/create')"
        />
      </div>
    </div>

    <!-- Filters -->
    <div class="card">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            T<PERSON><PERSON> kiếm
          </label>
          <InputText
            v-model="filters.search"
            placeholder="Mã đơn hàng, khách hàng..."
            class="w-full"
            @input="debouncedSearch"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Trạng thái đơn hàng
          </label>
          <Dropdown
            v-model="filters.trangThaiDonHang"
            :options="orderStatusOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Chọn trạng thái"
            class="w-full"
            showClear
            @change="searchOrders"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Trạng thái thanh toán
          </label>
          <Dropdown
            v-model="filters.trangThaiThanhToan"
            :options="paymentStatusOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Chọn trạng thái"
            class="w-full"
            showClear
            @change="searchOrders"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Loại đơn hàng
          </label>
          <Dropdown
            v-model="filters.loaiHoaDon"
            :options="orderTypeOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Chọn loại"
            class="w-full"
            showClear
            @change="searchOrders"
          />
        </div>
      </div>

      <!-- Date Range Filter -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Từ ngày
          </label>
          <Calendar
            v-model="filters.tuNgay"
            placeholder="Chọn ngày bắt đầu"
            class="w-full"
            dateFormat="dd/mm/yy"
            showIcon
            @date-select="searchOrders"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Đến ngày
          </label>
          <Calendar
            v-model="filters.denNgay"
            placeholder="Chọn ngày kết thúc"
            class="w-full"
            dateFormat="dd/mm/yy"
            showIcon
            @date-select="searchOrders"
          />
        </div>
      </div>
    </div>

    <!-- Orders DataTable -->
    <div class="card">
      <DataTable
        v-model:selection="selectedOrders"
        :value="orders"
        :loading="loading"
        dataKey="id"
        paginator
        :rows="pageSize"
        :totalRecords="totalRecords"
        :rowsPerPageOptions="[10, 25, 50, 100]"
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        currentPageReportTemplate="Hiển thị {first} đến {last} trong tổng số {totalRecords} đơn hàng"
        responsiveLayout="scroll"
        :globalFilterFields="['maHoaDon', 'khachHang.hoTen', 'khachHang.email']"
        selectionMode="multiple"
        :metaKeySelection="false"
        class="p-datatable-sm"
        lazy
        @page="onPage"
        @sort="onSort"
      >
        <template #header>
          <div class="flex justify-between items-center">
            <div class="flex items-center gap-3">
              <h3 class="text-lg font-semibold">Danh sách đơn hàng</h3>
              <Badge :value="totalRecords" severity="info" />
            </div>
            <div class="flex gap-2">
              <Button
                v-if="selectedOrders.length > 0"
                icon="pi pi-check"
                label="Xử lý hàng loạt"
                outlined
                @click="showBulkActionsDialog = true"
              />
              <Button
                icon="pi pi-download"
                label="Xuất Excel"
                outlined
                @click="exportToExcel"
              />
            </div>
          </div>
        </template>

        <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>

        <Column field="maHoaDon" header="Mã đơn hàng" sortable style="min-width: 140px">
          <template #body="{ data }">
            <div class="flex flex-col">
              <span class="font-mono text-sm font-medium">{{ data.maHoaDon }}</span>
              <span class="text-xs text-gray-500">{{ formatDate(data.ngayTao) }}</span>
            </div>
          </template>
        </Column>

        <Column field="khachHang" header="Khách hàng" style="min-width: 200px">
          <template #body="{ data }">
            <div class="flex items-center gap-3">
              <Avatar
                v-if="data.khachHang?.avatar"
                :image="data.khachHang.avatar"
                size="normal"
                shape="circle"
              />
              <Avatar
                v-else
                :label="data.khachHang?.hoTen?.charAt(0) || 'U'"
                size="normal"
                shape="circle"
                style="background-color: #dee2e6; color: #495057"
              />
              <div>
                <div class="font-medium">{{ data.khachHang?.hoTen || 'Khách vãng lai' }}</div>
                <div class="text-sm text-gray-500">{{ data.khachHang?.email || data.khachHang?.soDienThoai }}</div>
              </div>
            </div>
          </template>
        </Column>

        <Column field="loaiHoaDon" header="Loại" sortable style="min-width: 100px">
          <template #body="{ data }">
            <Tag
              :value="getOrderTypeText(data.loaiHoaDon)"
              :severity="data.loaiHoaDon === 'ONLINE' ? 'info' : 'secondary'"
            />
          </template>
        </Column>

        <Column field="tongThanhToan" header="Tổng tiền" sortable style="min-width: 120px">
          <template #body="{ data }">
            <span class="font-medium">{{ formatCurrency(data.tongThanhToan) }}</span>
          </template>
        </Column>

        <Column field="trangThaiDonHang" header="Trạng thái đơn hàng" sortable style="min-width: 140px">
          <template #body="{ data }">
            <Tag
              :value="getOrderStatusText(data.trangThaiDonHang)"
              :severity="getOrderStatusSeverity(data.trangThaiDonHang)"
            />
          </template>
        </Column>

        <Column field="trangThaiThanhToan" header="Thanh toán" sortable style="min-width: 130px">
          <template #body="{ data }">
            <Tag
              :value="getPaymentStatusText(data.trangThaiThanhToan)"
              :severity="getPaymentStatusSeverity(data.trangThaiThanhToan)"
            />
          </template>
        </Column>

        <Column header="Thao tác" style="min-width: 150px">
          <template #body="{ data }">
            <div class="flex gap-2">
              <Button
                icon="pi pi-eye"
                size="small"
                outlined
                @click="viewOrder(data)"
                v-tooltip.top="'Xem chi tiết'"
              />
              <Button
                icon="pi pi-pencil"
                size="small"
                outlined
                @click="editOrder(data)"
                v-tooltip.top="'Chỉnh sửa'"
                :disabled="data.trangThaiDonHang === 'DA_GIAO_HANG' || data.trangThaiDonHang === 'DA_HUY'"
              />
              <Button
                icon="pi pi-times"
                size="small"
                outlined
                severity="danger"
                @click="confirmCancelOrder(data)"
                v-tooltip.top="'Hủy đơn hàng'"
                :disabled="data.trangThaiDonHang === 'DA_GIAO_HANG' || data.trangThaiDonHang === 'DA_HUY'"
              />
            </div>
          </template>
        </Column>

        <template #empty>
          <div class="text-center py-8">
            <i class="pi pi-search text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-500">Không tìm thấy đơn hàng nào</p>
          </div>
        </template>
      </DataTable>
    </div>

    <!-- Cancel Order Dialog -->
    <Dialog v-model:visible="showCancelDialog" modal header="Hủy đơn hàng" :style="{ width: '500px' }">
      <div class="space-y-4">
        <p>Bạn có chắc chắn muốn hủy đơn hàng <strong>{{ orderToCancel?.maHoaDon }}</strong>?</p>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Lý do hủy
          </label>
          <Textarea
            v-model="cancelReason"
            placeholder="Nhập lý do hủy đơn hàng"
            rows="3"
            class="w-full"
          />
        </div>
      </div>

      <template #footer>
        <Button
          label="Không"
          outlined
          @click="showCancelDialog = false"
        />
        <Button
          label="Hủy đơn hàng"
          severity="danger"
          @click="cancelOrderConfirmed"
          :loading="cancelling"
        />
      </template>
    </Dialog>

    <!-- Confirmation Dialog -->
    <ConfirmDialog />
  </div>
</template>

<script setup lang="ts">
import type { Order, OrderSearchFilters, OrderStatus, PaymentStatus, OrderType } from '~/composables/useOrder'

definePageMeta({
  middleware: 'auth'
})

const {
  searchOrders,
  cancelOrder,
  getOrderStatusText,
  getPaymentStatusText,
  getOrderTypeText,
  getOrderStatusSeverity,
  getPaymentStatusSeverity
} = useOrder()
const confirm = useConfirm()
const toast = useToast()
const router = useRouter()

// Reactive data
const loading = ref(false)
const orders = ref<Order[]>([])
const selectedOrders = ref<Order[]>([])
const totalRecords = ref(0)
const pageSize = ref(10)
const currentPage = ref(0)

// Filters
const filters = ref<OrderSearchFilters>({
  search: '',
  trangThaiDonHang: undefined,
  trangThaiThanhToan: undefined,
  loaiHoaDon: undefined,
  tuNgay: null,
  denNgay: null
})

// Dialog states
const showCancelDialog = ref(false)
const orderToCancel = ref<Order | null>(null)
const cancelReason = ref('')
const cancelling = ref(false)

// Filter options
const orderStatusOptions = [
  { label: 'Chờ xác nhận', value: 'CHO_XAC_NHAN' },
  { label: 'Đã xác nhận', value: 'DA_XAC_NHAN' },
  { label: 'Đang chuẩn bị', value: 'DANG_CHUAN_BI' },
  { label: 'Đang giao hàng', value: 'DANG_GIAO_HANG' },
  { label: 'Đã giao hàng', value: 'DA_GIAO_HANG' },
  { label: 'Đã hủy', value: 'DA_HUY' },
  { label: 'Trả hàng', value: 'TRA_HANG' }
]

const paymentStatusOptions = [
  { label: 'Chưa thanh toán', value: 'CHUA_THANH_TOAN' },
  { label: 'Đã thanh toán', value: 'DA_THANH_TOAN' },
  { label: 'Thanh toán một phần', value: 'THANH_TOAN_MOT_PHAN' },
  { label: 'Hoàn tiền', value: 'HOAN_TIEN' }
]

const orderTypeOptions = [
  { label: 'Trực tuyến', value: 'ONLINE' },
  { label: 'Tại quầy', value: 'TAI_QUAY' }
]

// Debounced search
const debouncedSearch = useDebounceFn(() => {
  loadOrders()
}, 500)

/**
 * Load orders data
 */
const loadOrders = async () => {
  loading.value = true
  try {
    const searchFilters = { ...filters.value }

    // Format dates for API
    if (searchFilters.tuNgay) {
      searchFilters.tuNgay = formatDateForAPI(searchFilters.tuNgay)
    }
    if (searchFilters.denNgay) {
      searchFilters.denNgay = formatDateForAPI(searchFilters.denNgay)
    }

    // Remove empty values
    Object.keys(searchFilters).forEach(key => {
      if (searchFilters[key] === '' || searchFilters[key] === null || searchFilters[key] === undefined) {
        delete searchFilters[key]
      }
    })

    const result = await searchOrders(searchFilters, currentPage.value, pageSize.value)
    orders.value = result.content
    totalRecords.value = result.totalElements
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Không thể tải dữ liệu đơn hàng',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

/**
 * Search orders with current filters
 */
const searchOrdersData = () => {
  currentPage.value = 0
  loadOrders()
}

/**
 * Refresh data
 */
const refreshData = () => {
  // Reset filters
  filters.value = {
    search: '',
    trangThaiDonHang: undefined,
    trangThaiThanhToan: undefined,
    loaiHoaDon: undefined,
    tuNgay: null,
    denNgay: null
  }

  currentPage.value = 0
  loadOrders()
}

/**
 * Handle pagination
 */
const onPage = (event: any) => {
  currentPage.value = event.page
  pageSize.value = event.rows
  loadOrders()
}

/**
 * Handle sorting
 */
const onSort = (event: any) => {
  // Add sorting logic here if needed
  loadOrders()
}

/**
 * View order details
 */
const viewOrder = (order: Order) => {
  router.push(`/orders/${order.id}`)
}

/**
 * Edit order
 */
const editOrder = (order: Order) => {
  router.push(`/orders/${order.id}/edit`)
}

/**
 * Confirm cancel order
 */
const confirmCancelOrder = (order: Order) => {
  orderToCancel.value = order
  cancelReason.value = ''
  showCancelDialog.value = true
}

/**
 * Cancel order confirmed
 */
const cancelOrderConfirmed = async () => {
  if (!orderToCancel.value) return

  cancelling.value = true
  try {
    await cancelOrder(orderToCancel.value.id!, cancelReason.value)
    await loadOrders()
    showCancelDialog.value = false
    selectedOrders.value = []
  } catch (error) {
    // Error is handled by the composable
  } finally {
    cancelling.value = false
  }
}

/**
 * Export to Excel
 */
const exportToExcel = () => {
  toast.add({
    severity: 'info',
    summary: 'Thông tin',
    detail: 'Tính năng xuất Excel sẽ được triển khai trong phiên bản tiếp theo',
    life: 3000
  })
}

/**
 * Format date for API
 */
const formatDateForAPI = (date: Date): string => {
  return date.toISOString().split('T')[0]
}

// Load data on mount
onMounted(() => {
  loadOrders()
})

// Set page title
useHead({
  title: 'Quản lý đơn hàng - LapXpert Admin'
})
</script>
