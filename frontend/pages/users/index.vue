<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Qu<PERSON>n lý người dùng
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Quản lý tài khoản người dùng trong hệ thống
        </p>
      </div>
      <div class="flex gap-3">
        <Button
          icon="pi pi-refresh"
          label="Làm mới"
          outlined
          @click="refreshData"
          :loading="loading"
        />
        <Button
          icon="pi pi-plus"
          label="Thêm người dùng"
          @click="navigateTo('/users/create')"
        />
      </div>
    </div>

    <!-- User Type Tabs -->
    <div class="card">
      <TabView v-model:activeIndex="activeTab" @tab-change="onTabChange">
        <TabPanel header="Khách hàng">
          <div class="space-y-4">
            <!-- Customer Filters -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tìm kiếm khách hàng
                </label>
                <InputText
                  v-model="customerSearch"
                  placeholder="Tên, email, số điện thoại..."
                  class="w-full"
                  @input="debouncedCustomerSearch"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Trạng thái
                </label>
                <Dropdown
                  v-model="customerStatusFilter"
                  :options="statusOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Chọn trạng thái"
                  class="w-full"
                  showClear
                  @change="loadCustomers"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Giới tính
                </label>
                <Dropdown
                  v-model="customerGenderFilter"
                  :options="genderOptions"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Chọn giới tính"
                  class="w-full"
                  showClear
                  @change="loadCustomers"
                />
              </div>
            </div>

            <!-- Customer DataTable -->
            <DataTable
              v-model:selection="selectedCustomers"
              :value="customers"
              :loading="loading"
              dataKey="id"
              paginator
              :rows="10"
              :rowsPerPageOptions="[5, 10, 25, 50]"
              paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
              currentPageReportTemplate="Hiển thị {first} đến {last} trong tổng số {totalRecords} khách hàng"
              responsiveLayout="scroll"
              :globalFilterFields="['hoTen', 'email', 'soDienThoai']"
              selectionMode="multiple"
              :metaKeySelection="false"
              class="p-datatable-sm"
            >
              <template #header>
                <div class="flex justify-between items-center">
                  <div class="flex items-center gap-3">
                    <h3 class="text-lg font-semibold">Danh sách khách hàng</h3>
                    <Badge :value="customers.length" severity="info" />
                  </div>
                  <div class="flex gap-2">
                    <Button
                      v-if="selectedCustomers.length > 0"
                      icon="pi pi-trash"
                      label="Xóa đã chọn"
                      severity="danger"
                      outlined
                      @click="confirmDeleteSelectedCustomers"
                    />
                    <Button
                      icon="pi pi-download"
                      label="Xuất Excel"
                      outlined
                      @click="exportCustomers"
                    />
                  </div>
                </div>
              </template>

              <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>

              <Column field="hoTen" header="Khách hàng" sortable style="min-width: 200px">
                <template #body="{ data }">
                  <div class="flex items-center gap-3">
                    <Avatar
                      v-if="data.avatar"
                      :image="data.avatar"
                      size="normal"
                      shape="circle"
                    />
                    <Avatar
                      v-else
                      :label="data.hoTen?.charAt(0) || 'U'"
                      size="normal"
                      shape="circle"
                      style="background-color: #dee2e6; color: #495057"
                    />
                    <div>
                      <div class="font-medium">{{ data.hoTen }}</div>
                      <div class="text-sm text-gray-500">{{ data.maNguoiDung }}</div>
                    </div>
                  </div>
                </template>
              </Column>

              <Column field="email" header="Email" sortable style="min-width: 200px">
                <template #body="{ data }">
                  <div>
                    <div class="font-medium">{{ data.email }}</div>
                    <div class="text-sm text-gray-500">{{ data.soDienThoai }}</div>
                  </div>
                </template>
              </Column>

              <Column field="gioiTinh" header="Giới tính" style="min-width: 100px">
                <template #body="{ data }">
                  <span v-if="data.gioiTinh">{{ getGenderText(data.gioiTinh) }}</span>
                  <span v-else class="text-gray-400">Chưa có</span>
                </template>
              </Column>

              <Column field="trangThai" header="Trạng thái" sortable style="min-width: 120px">
                <template #body="{ data }">
                  <Tag
                    :value="getUserStatusText(data.trangThai)"
                    :severity="getUserStatusSeverity(data.trangThai)"
                  />
                </template>
              </Column>

              <Column field="ngayTao" header="Ngày tạo" sortable style="min-width: 120px">
                <template #body="{ data }">
                  <div class="text-sm">
                    <div>{{ formatDate(data.ngayTao) }}</div>
                    <div class="text-gray-500">{{ data.nguoiTao || 'Hệ thống' }}</div>
                  </div>
                </template>
              </Column>

              <Column header="Thao tác" style="min-width: 150px">
                <template #body="{ data }">
                  <div class="flex gap-2">
                    <Button
                      icon="pi pi-eye"
                      size="small"
                      outlined
                      @click="viewUser(data)"
                      v-tooltip.top="'Xem chi tiết'"
                    />
                    <Button
                      icon="pi pi-pencil"
                      size="small"
                      outlined
                      @click="editUser(data)"
                      v-tooltip.top="'Chỉnh sửa'"
                    />
                    <Button
                      icon="pi pi-trash"
                      size="small"
                      outlined
                      severity="danger"
                      @click="confirmDeleteCustomer(data)"
                      v-tooltip.top="'Xóa'"
                    />
                  </div>
                </template>
              </Column>

              <template #empty>
                <div class="text-center py-8">
                  <i class="pi pi-users text-4xl text-gray-400 mb-4"></i>
                  <p class="text-gray-500">Không tìm thấy khách hàng nào</p>
                </div>
              </template>
            </DataTable>
          </div>
        </TabPanel>

        <TabPanel header="Nhân viên">
          <div class="space-y-4">
            <!-- Staff DataTable -->
            <DataTable
              v-model:selection="selectedStaff"
              :value="staff"
              :loading="loading"
              dataKey="id"
              paginator
              :rows="10"
              :rowsPerPageOptions="[5, 10, 25, 50]"
              paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
              currentPageReportTemplate="Hiển thị {first} đến {last} trong tổng số {totalRecords} nhân viên"
              responsiveLayout="scroll"
              :globalFilterFields="['hoTen', 'email', 'soDienThoai']"
              selectionMode="multiple"
              :metaKeySelection="false"
              class="p-datatable-sm"
            >
              <template #header>
                <div class="flex justify-between items-center">
                  <div class="flex items-center gap-3">
                    <h3 class="text-lg font-semibold">Danh sách nhân viên</h3>
                    <Badge :value="staff.length" severity="info" />
                  </div>
                  <div class="flex gap-2">
                    <Button
                      v-if="selectedStaff.length > 0"
                      icon="pi pi-trash"
                      label="Xóa đã chọn"
                      severity="danger"
                      outlined
                      @click="confirmDeleteSelectedStaff"
                    />
                    <Button
                      icon="pi pi-download"
                      label="Xuất Excel"
                      outlined
                      @click="exportStaff"
                    />
                  </div>
                </div>
              </template>

              <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>

              <Column field="hoTen" header="Nhân viên" sortable style="min-width: 200px">
                <template #body="{ data }">
                  <div class="flex items-center gap-3">
                    <Avatar
                      v-if="data.avatar"
                      :image="data.avatar"
                      size="normal"
                      shape="circle"
                    />
                    <Avatar
                      v-else
                      :label="data.hoTen?.charAt(0) || 'U'"
                      size="normal"
                      shape="circle"
                      style="background-color: #3B82F6; color: white"
                    />
                    <div>
                      <div class="font-medium">{{ data.hoTen }}</div>
                      <div class="text-sm text-gray-500">{{ data.maNguoiDung }}</div>
                    </div>
                  </div>
                </template>
              </Column>

              <Column field="vaiTro" header="Vai trò" sortable style="min-width: 120px">
                <template #body="{ data }">
                  <Tag
                    :value="getUserRoleText(data.vaiTro)"
                    :severity="getUserRoleSeverity(data.vaiTro)"
                  />
                </template>
              </Column>

              <Column field="trangThai" header="Trạng thái" sortable style="min-width: 120px">
                <template #body="{ data }">
                  <Tag
                    :value="getUserStatusText(data.trangThai)"
                    :severity="getUserStatusSeverity(data.trangThai)"
                  />
                </template>
              </Column>

              <Column header="Thao tác" style="min-width: 150px">
                <template #body="{ data }">
                  <div class="flex gap-2">
                    <Button
                      icon="pi pi-eye"
                      size="small"
                      outlined
                      @click="viewUser(data)"
                      v-tooltip.top="'Xem chi tiết'"
                    />
                    <Button
                      icon="pi pi-pencil"
                      size="small"
                      outlined
                      @click="editUser(data)"
                      v-tooltip.top="'Chỉnh sửa'"
                    />
                    <Button
                      icon="pi pi-trash"
                      size="small"
                      outlined
                      severity="danger"
                      @click="confirmDeleteStaff(data)"
                      v-tooltip.top="'Xóa'"
                    />
                  </div>
                </template>
              </Column>

              <template #empty>
                <div class="text-center py-8">
                  <i class="pi pi-users text-4xl text-gray-400 mb-4"></i>
                  <p class="text-gray-500">Không tìm thấy nhân viên nào</p>
                </div>
              </template>
            </DataTable>
          </div>
        </TabPanel>
      </TabView>
    </div>

    <!-- Delete Confirmation Dialog -->
    <ConfirmDialog />
  </div>
</template>

<script setup lang="ts">
import type { Customer, Staff, UserStatus, Gender } from '~/composables/useUser'

definePageMeta({
  middleware: 'auth'
})

const {
  getAllCustomers,
  getAllStaff,
  deleteCustomer,
  deleteStaff,
  getUserRoleText,
  getUserStatusText,
  getGenderText,
  getUserRoleSeverity,
  getUserStatusSeverity
} = useUser()
const confirm = useConfirm()
const toast = useToast()
const router = useRouter()

// Reactive data
const loading = ref(false)
const activeTab = ref(0)
const customers = ref<Customer[]>([])
const staff = ref<Staff[]>([])
const selectedCustomers = ref<Customer[]>([])
const selectedStaff = ref<Staff[]>([])

// Filters
const customerSearch = ref('')
const customerStatusFilter = ref<UserStatus | null>(null)
const customerGenderFilter = ref<Gender | null>(null)

// Filter options
const statusOptions = [
  { label: 'Hoạt động', value: 'HOAT_DONG' },
  { label: 'Không hoạt động', value: 'KHONG_HOAT_DONG' },
  { label: 'Bị khóa', value: 'BI_KHOA' },
  { label: 'Chờ xác thực', value: 'CHO_XAC_THUC' }
]

const genderOptions = [
  { label: 'Nam', value: 'NAM' },
  { label: 'Nữ', value: 'NU' },
  { label: 'Khác', value: 'KHAC' }
]

// Debounced search
const debouncedCustomerSearch = useDebounceFn(() => {
  loadCustomers()
}, 500)

/**
 * Load customers data
 */
const loadCustomers = async () => {
  loading.value = true
  try {
    const searchTerm = customerSearch.value.trim()
    customers.value = await getAllCustomers(searchTerm || undefined)

    // Apply filters
    if (customerStatusFilter.value) {
      customers.value = customers.value.filter(c => c.trangThai === customerStatusFilter.value)
    }
    if (customerGenderFilter.value) {
      customers.value = customers.value.filter(c => c.gioiTinh === customerGenderFilter.value)
    }
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Không thể tải dữ liệu khách hàng',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

/**
 * Load staff data
 */
const loadStaff = async () => {
  loading.value = true
  try {
    staff.value = await getAllStaff()
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: 'Lỗi',
      detail: 'Không thể tải dữ liệu nhân viên',
      life: 5000
    })
  } finally {
    loading.value = false
  }
}

/**
 * Handle tab change
 */
const onTabChange = (event: any) => {
  activeTab.value = event.index
  if (event.index === 0) {
    loadCustomers()
  } else if (event.index === 1) {
    loadStaff()
  }
}

/**
 * Refresh data
 */
const refreshData = () => {
  if (activeTab.value === 0) {
    loadCustomers()
  } else if (activeTab.value === 1) {
    loadStaff()
  }
}

/**
 * View user details
 */
const viewUser = (user: Customer | Staff) => {
  router.push(`/users/${user.id}`)
}

/**
 * Edit user
 */
const editUser = (user: Customer | Staff) => {
  router.push(`/users/${user.id}/edit`)
}

/**
 * Confirm delete customer
 */
const confirmDeleteCustomer = (customer: Customer) => {
  confirm.require({
    message: `Bạn có chắc chắn muốn xóa khách hàng "${customer.hoTen}"?`,
    header: 'Xác nhận xóa',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Hủy',
    acceptLabel: 'Xóa',
    accept: () => deleteCustomerData(customer.id!)
  })
}

/**
 * Confirm delete staff
 */
const confirmDeleteStaff = (staffMember: Staff) => {
  confirm.require({
    message: `Bạn có chắc chắn muốn xóa nhân viên "${staffMember.hoTen}"?`,
    header: 'Xác nhận xóa',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Hủy',
    acceptLabel: 'Xóa',
    accept: () => deleteStaffData(staffMember.id!)
  })
}

/**
 * Delete customer
 */
const deleteCustomerData = async (id: number) => {
  try {
    await deleteCustomer(id)
    await loadCustomers()
    selectedCustomers.value = []
  } catch (error) {
    // Error is handled by the composable
  }
}

/**
 * Delete staff
 */
const deleteStaffData = async (id: number) => {
  try {
    await deleteStaff(id)
    await loadStaff()
    selectedStaff.value = []
  } catch (error) {
    // Error is handled by the composable
  }
}

/**
 * Confirm delete selected customers
 */
const confirmDeleteSelectedCustomers = () => {
  confirm.require({
    message: `Bạn có chắc chắn muốn xóa ${selectedCustomers.value.length} khách hàng đã chọn?`,
    header: 'Xác nhận xóa',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Hủy',
    acceptLabel: 'Xóa',
    accept: () => deleteSelectedCustomers()
  })
}

/**
 * Confirm delete selected staff
 */
const confirmDeleteSelectedStaff = () => {
  confirm.require({
    message: `Bạn có chắc chắn muốn xóa ${selectedStaff.value.length} nhân viên đã chọn?`,
    header: 'Xác nhận xóa',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    rejectLabel: 'Hủy',
    acceptLabel: 'Xóa',
    accept: () => deleteSelectedStaff()
  })
}

/**
 * Delete selected customers
 */
const deleteSelectedCustomers = async () => {
  try {
    await Promise.all(
      selectedCustomers.value.map(customer => deleteCustomer(customer.id!))
    )
    await loadCustomers()
    selectedCustomers.value = []
  } catch (error) {
    // Error is handled by the composable
  }
}

/**
 * Delete selected staff
 */
const deleteSelectedStaff = async () => {
  try {
    await Promise.all(
      selectedStaff.value.map(staffMember => deleteStaff(staffMember.id!))
    )
    await loadStaff()
    selectedStaff.value = []
  } catch (error) {
    // Error is handled by the composable
  }
}

/**
 * Export functions
 */
const exportCustomers = () => {
  toast.add({
    severity: 'info',
    summary: 'Thông tin',
    detail: 'Tính năng xuất Excel sẽ được triển khai trong phiên bản tiếp theo',
    life: 3000
  })
}

const exportStaff = () => {
  toast.add({
    severity: 'info',
    summary: 'Thông tin',
    detail: 'Tính năng xuất Excel sẽ được triển khai trong phiên bản tiếp theo',
    life: 3000
  })
}

// Load data on mount
onMounted(() => {
  loadCustomers() // Load customers by default
})

// Set page title
useHead({
  title: 'Quản lý người dùng - LapXpert Admin'
})
</script>
