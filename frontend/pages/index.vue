<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Chào mừng trở lại, {{ currentUser?.hoTen || 'Admin' }}!
        </p>
      </div>
      <div class="flex items-center gap-3">
        <Button
          icon="pi pi-refresh"
          label="Refresh"
          outlined
          @click="refreshData"
        />
        <Button
          icon="pi pi-download"
          label="Export"
          @click="exportData"
        />
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="card">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Tổng doanh thu
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ formatCurrency(stats.totalRevenue) }}
            </p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
            <i class="pi pi-dollar text-green-600 dark:text-green-400 text-xl"></i>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span class="text-green-600 text-sm font-medium">+12.5%</span>
          <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">so với tháng trước</span>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Đơn hàng
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ stats.totalOrders }}
            </p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
            <i class="pi pi-shopping-cart text-blue-600 dark:text-blue-400 text-xl"></i>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span class="text-blue-600 text-sm font-medium">+8.2%</span>
          <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">so với tháng trước</span>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Sản phẩm
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ stats.totalProducts }}
            </p>
          </div>
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
            <i class="pi pi-desktop text-purple-600 dark:text-purple-400 text-xl"></i>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span class="text-purple-600 text-sm font-medium">+5.1%</span>
          <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">so với tháng trước</span>
        </div>
      </div>

      <div class="card">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Khách hàng
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ stats.totalCustomers }}
            </p>
          </div>
          <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
            <i class="pi pi-users text-orange-600 dark:text-orange-400 text-xl"></i>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span class="text-orange-600 text-sm font-medium">+15.3%</span>
          <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">so với tháng trước</span>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Doanh thu theo tháng</h3>
        </div>
        <div class="card-content">
          <div class="h-64">
            <LineChart
              :data="revenueChartData"
              :height="256"
            />
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Đơn hàng gần đây</h3>
        </div>
        <div class="card-content">
          <div class="space-y-4">
            <div v-for="order in recentOrders" :key="order.id" class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <p class="font-medium text-gray-900 dark:text-white">
                  #{{ order.id }}
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {{ order.customerName }}
                </p>
              </div>
              <div class="text-right">
                <p class="font-medium text-gray-900 dark:text-white">
                  {{ formatCurrency(order.total) }}
                </p>
                <span :class="getStatusClass(order.status)" class="text-xs px-2 py-1 rounded-full">
                  {{ order.status }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: 'auth'
})

const { currentUser } = useAuth()
const { generateRevenueData } = useChart()

// Mock data - will be replaced with real API calls
const stats = reactive({
  totalRevenue: 125000000,
  totalOrders: 1234,
  totalProducts: 567,
  totalCustomers: 890
})

// Chart data
const revenueChartData = computed(() => generateRevenueData('tháng này'))

const recentOrders = ref([
  { id: '12345', customerName: 'Nguyễn Văn A', total: 25000000, status: 'Đã giao' },
  { id: '12346', customerName: 'Trần Thị B', total: 18000000, status: 'Đang xử lý' },
  { id: '12347', customerName: 'Lê Văn C', total: 32000000, status: 'Chờ thanh toán' },
  { id: '12348', customerName: 'Phạm Thị D', total: 15000000, status: 'Đã hủy' }
])

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}

const getStatusClass = (status) => {
  const classes = {
    'Đã giao': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'Đang xử lý': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'Chờ thanh toán': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'Đã hủy': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const refreshData = () => {
  // TODO: Implement data refresh logic using useStatistics composable
  // const { getDashboardSummary } = useStatistics()
  // await getDashboardSummary()
}

const exportData = () => {
  // TODO: Implement data export logic
  // Generate CSV/Excel export of dashboard data
}

// Set page title
useHead({
  title: 'Dashboard - LapXpert Admin'
})
</script>
