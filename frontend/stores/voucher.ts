/**
 * Voucher store for LapXpert Admin Dashboard
 * Centralized state management for voucher operations
 */

import type { Voucher, VoucherFilters, VoucherStats } from '~/composables/useVoucher'

export const useVoucherStore = defineStore('voucher', () => {
  const { 
    vouchers,
    currentVoucher,
    loading,
    error,
    filteredVouchers,
    voucherStats,
    appliedVouchers,
    getTotalDiscount,
    getAppliedVoucherCodes,
    fetchVouchers,
    fetchVoucherById,
    createVoucher,
    updateVoucher,
    deleteVoucher,
    validateVoucher,
    searchVouchers,
    getAvailableVouchers,
    findBestVoucher,
    addVoucherToOrder,
    removeVoucherFromOrder,
    clearAppliedVouchers,
    setFilters,
    clearFilters,
    clearError,
    formatDiscountValue,
    calculateDiscountAmount,
    isVoucherApplied,
    getVoucherByCode,
    getVoucherStatusInfo,
    generateVoucherCode
  } = useVoucher()

  // Additional store-specific state
  const selectedVouchers = ref<Voucher[]>([])
  const bulkOperationLoading = ref(false)
  const lastFetchTime = ref<Date | null>(null)
  const cacheTimeout = 5 * 60 * 1000 // 5 minutes

  // Store-specific computed properties
  const hasSelectedVouchers = computed(() => selectedVouchers.value.length > 0)
  
  const selectedVoucherIds = computed(() => 
    selectedVouchers.value.map(v => v.id).filter(Boolean) as number[]
  )

  const isDataStale = computed(() => {
    if (!lastFetchTime.value) return true
    return Date.now() - lastFetchTime.value.getTime() > cacheTimeout
  })

  // Enhanced methods with store-specific logic
  const refreshVouchers = async (force = false) => {
    if (!force && !isDataStale.value) {
      return vouchers.value
    }

    await fetchVouchers()
    lastFetchTime.value = new Date()
    return vouchers.value
  }

  const createVoucherWithRefresh = async (voucherData: Omit<Voucher, 'id'>) => {
    const result = await createVoucher(voucherData)
    if (result) {
      lastFetchTime.value = new Date()
    }
    return result
  }

  const updateVoucherWithRefresh = async (id: number, voucherData: Partial<Voucher>) => {
    const result = await updateVoucher(id, voucherData)
    if (result) {
      lastFetchTime.value = new Date()
    }
    return result
  }

  const deleteVoucherWithRefresh = async (id: number, reason?: string) => {
    const result = await deleteVoucher(id, reason)
    if (result) {
      // Remove from selected vouchers if it was selected
      selectedVouchers.value = selectedVouchers.value.filter(v => v.id !== id)
      lastFetchTime.value = new Date()
    }
    return result
  }

  const bulkDeleteVouchers = async (ids: number[], reason?: string) => {
    bulkOperationLoading.value = true
    const results = []

    for (const id of ids) {
      try {
        const result = await deleteVoucher(id, reason)
        results.push({ id, success: result })
      } catch (error) {
        results.push({ id, success: false, error })
      }
    }

    // Clear selected vouchers
    selectedVouchers.value = []
    bulkOperationLoading.value = false
    lastFetchTime.value = new Date()

    return results
  }

  const selectVoucher = (voucher: Voucher) => {
    const index = selectedVouchers.value.findIndex(v => v.id === voucher.id)
    if (index === -1) {
      selectedVouchers.value.push(voucher)
    }
  }

  const unselectVoucher = (voucher: Voucher) => {
    selectedVouchers.value = selectedVouchers.value.filter(v => v.id !== voucher.id)
  }

  const toggleVoucherSelection = (voucher: Voucher) => {
    const index = selectedVouchers.value.findIndex(v => v.id === voucher.id)
    if (index === -1) {
      selectedVouchers.value.push(voucher)
    } else {
      selectedVouchers.value.splice(index, 1)
    }
  }

  const selectAllVouchers = () => {
    selectedVouchers.value = [...filteredVouchers.value]
  }

  const clearSelection = () => {
    selectedVouchers.value = []
  }

  const isVoucherSelected = (voucher: Voucher) => {
    return selectedVouchers.value.some(v => v.id === voucher.id)
  }

  // Voucher analytics methods
  const getVoucherUsageRate = (voucher: Voucher) => {
    if (voucher.soLuongBanDau === 0) return 0
    return (voucher.soLuongDaDung / voucher.soLuongBanDau) * 100
  }

  const getVoucherRemainingDays = (voucher: Voucher) => {
    const endDate = new Date(voucher.ngayKetThuc)
    const today = new Date()
    const diffTime = endDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return Math.max(0, diffDays)
  }

  const getVoucherEffectiveness = (voucher: Voucher) => {
    const usageRate = getVoucherUsageRate(voucher)
    const remainingDays = getVoucherRemainingDays(voucher)
    
    if (voucher.trangThai === 'DA_KET_THUC') {
      return usageRate >= 80 ? 'Hiệu quả cao' : usageRate >= 50 ? 'Hiệu quả trung bình' : 'Hiệu quả thấp'
    }
    
    if (remainingDays === 0) {
      return 'Sắp hết hạn'
    }
    
    return usageRate >= 60 ? 'Đang hoạt động tốt' : 'Cần cải thiện'
  }

  // Filter presets
  const applyFilterPreset = (preset: string) => {
    switch (preset) {
      case 'active':
        setFilters({ status: 'DA_DIEN_RA', type: 'all', customerType: 'all' })
        break
      case 'expired':
        setFilters({ status: 'DA_KET_THUC', type: 'all', customerType: 'all' })
        break
      case 'percentage':
        setFilters({ status: 'all', type: 'PHAN_TRAM', customerType: 'all' })
        break
      case 'fixed':
        setFilters({ status: 'all', type: 'SO_TIEN_CO_DINH', customerType: 'all' })
        break
      case 'public':
        setFilters({ status: 'all', type: 'all', customerType: 'public' })
        break
      case 'private':
        setFilters({ status: 'all', type: 'all', customerType: 'private' })
        break
      default:
        clearFilters()
    }
  }

  // Export/Import functionality
  const exportVouchers = (format: 'csv' | 'excel' = 'csv') => {
    const data = filteredVouchers.value.map(voucher => ({
      'Mã voucher': voucher.maPhieuGiamGia,
      'Loại giảm giá': voucher.loaiGiamGia === 'PHAN_TRAM' ? 'Phần trăm' : 'Số tiền cố định',
      'Giá trị giảm': voucher.giaTriGiam,
      'Đơn hàng tối thiểu': voucher.giaTriDonHangToiThieu || 0,
      'Ngày bắt đầu': new Date(voucher.ngayBatDau).toLocaleDateString('vi-VN'),
      'Ngày kết thúc': new Date(voucher.ngayKetThuc).toLocaleDateString('vi-VN'),
      'Số lượng ban đầu': voucher.soLuongBanDau,
      'Số lượng đã dùng': voucher.soLuongDaDung,
      'Trạng thái': getVoucherStatusInfo(voucher.trangThai).label,
      'Mô tả': voucher.moTa || ''
    }))

    if (format === 'csv') {
      downloadCSV(data, 'vouchers.csv')
    } else {
      downloadExcel(data, 'vouchers.xlsx')
    }
  }

  const downloadCSV = (data: any[], filename: string) => {
    const csv = convertToCSV(data)
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    link.click()
  }

  const downloadExcel = (data: any[], filename: string) => {
    // This would require a library like xlsx
    console.log('Excel export not implemented yet', data, filename)
  }

  const convertToCSV = (data: any[]) => {
    if (!data.length) return ''
    
    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n')
    
    return csvContent
  }

  return {
    // State
    vouchers,
    currentVoucher,
    loading,
    error,
    selectedVouchers: readonly(selectedVouchers),
    bulkOperationLoading: readonly(bulkOperationLoading),
    appliedVouchers,

    // Computed
    filteredVouchers,
    voucherStats,
    getTotalDiscount,
    getAppliedVoucherCodes,
    hasSelectedVouchers,
    selectedVoucherIds,
    isDataStale,

    // CRUD Methods
    refreshVouchers,
    fetchVoucherById,
    createVoucherWithRefresh,
    updateVoucherWithRefresh,
    deleteVoucherWithRefresh,
    bulkDeleteVouchers,

    // Selection Methods
    selectVoucher,
    unselectVoucher,
    toggleVoucherSelection,
    selectAllVouchers,
    clearSelection,
    isVoucherSelected,

    // Validation & Search
    validateVoucher,
    searchVouchers,
    getAvailableVouchers,
    findBestVoucher,

    // Order Integration
    addVoucherToOrder,
    removeVoucherFromOrder,
    clearAppliedVouchers,

    // Analytics
    getVoucherUsageRate,
    getVoucherRemainingDays,
    getVoucherEffectiveness,

    // Filters
    setFilters,
    clearFilters,
    applyFilterPreset,

    // Utility Methods
    clearError,
    formatDiscountValue,
    calculateDiscountAmount,
    isVoucherApplied,
    getVoucherByCode,
    getVoucherStatusInfo,
    generateVoucherCode,
    exportVouchers
  }
})
