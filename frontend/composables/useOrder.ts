/**
 * Order composable for LapXpert Admin Dashboard
 * Provides comprehensive order management functionality
 */

export interface Order {
  id?: number
  maHoaDon?: string
  // Backend returns IDs, frontend can populate full objects separately
  khachHangId?: number
  nhanVienId?: number
  khachHang?: Customer // Optional full object for display
  nhanVien?: Staff // Optional full object for display
  // Delivery address - can be ID or full object
  diaChiGiaoHangId?: number
  diaChiGiaoHang?: Address
  // Delivery contact information (can be different from account holder)
  nguoiNhanTen?: string
  nguoiNhanSdt?: string
  ngayTao?: string
  ngayCapNhat?: string
  // Fixed field names to match backend DTO
  tongTienHang?: number // Was: tongTien
  giaTriGiamGiaVoucher?: number // Was: tongGiamGia
  phiVanChuyen?: number
  tongThanhToan?: number
  trangThaiDonHang?: OrderStatus
  trangThaiThanhToan?: PaymentStatus
  loaiHoaDon?: OrderType
  // These fields exist in entity but not exposed in DTO
  maVanDon?: string
  ngayDuKienGiaoHang?: string
  ghiChu?: string
  // Fixed field name to match backend DTO
  chiTiet?: OrderItem[] // Was: hoaDonChiTiets
  hoaDonPhieuGiamGias?: OrderVoucher[]
  // Voucher codes for order creation
  voucherCodes?: string[]
  nguoiTao?: string
  nguoiCapNhat?: string
}

export interface OrderItem {
  id?: number
  hoaDonId?: number // Corresponds to HoaDon entity
  sanPhamChiTietId?: number // Corresponds to SanPhamChiTiet entity
  sanPhamChiTiet?: ProductVariant // Optional full object for display
  soLuong: number
  giaGoc?: number // Original price
  giaBan: number // Selling price (was: donGia)
  thanhTien?: number // Total amount
  // Snapshot fields for order history
  tenSanPhamSnapshot?: string
  skuSnapshot?: string
  hinhAnhSnapshot?: string
  ngayTao?: string
  ngayCapNhat?: string
  ghiChu?: string
}

export interface OrderVoucher {
  id?: number
  phieuGiamGia?: Voucher
  soTienGiam?: number
}

export interface Customer {
  id?: number
  hoTen?: string
  email?: string
  soDienThoai?: string
  avatar?: string
}

export interface Staff {
  id?: number
  hoTen?: string
  email?: string
  chucVu?: string
}

export interface Address {
  id?: number
  hoTen?: string
  soDienThoai?: string
  diaChiChiTiet?: string
  phuongXa?: string
  quanHuyen?: string
  tinhThanhPho?: string
  loaiDiaChi?: string
}

export interface ProductVariant {
  id?: number
  sanPham?: Product
  sku?: string
  giaBan?: number
  hinhAnh?: string
}

export interface Product {
  id?: number
  tenSanPham?: string
  hinhAnh?: string[]
}

export interface Voucher {
  id?: number
  maPhieu?: string
  tenPhieu?: string
  loaiGiamGia?: string
  giaTriGiam?: number
}

export interface OrderAuditHistory {
  id?: number
  hoaDonId?: number
  hanhDong?: string
  thoiGianThayDoi?: string
  nguoiThucHien?: string
  lyDoThayDoi?: string
  giaTriCu?: string
  giaTriMoi?: string
  hanhDongDisplay?: string
  thoiGianThayDoiVietnam?: string
  chiTietThayDoi?: ChangeDetail[]
}

export interface ChangeDetail {
  fieldName?: string
  fieldDisplayName?: string
  oldValue?: string
  newValue?: string
  oldValueDisplay?: string
  newValueDisplay?: string
}

export interface TimelineEntry {
  action?: string
  actionDisplay?: string
  timestamp?: string
  timestampDisplay?: string
  user?: string
  reason?: string
  changes?: ChangeDetail[]
  icon?: string
  severity?: 'success' | 'info' | 'warn' | 'error'
}

export enum OrderStatus {
  CHO_XAC_NHAN = 'CHO_XAC_NHAN',
  DA_XAC_NHAN = 'DA_XAC_NHAN',
  DANG_CHUAN_BI = 'DANG_CHUAN_BI',
  DANG_GIAO_HANG = 'DANG_GIAO_HANG',
  DA_GIAO_HANG = 'DA_GIAO_HANG',
  DA_HUY = 'DA_HUY',
  TRA_HANG = 'TRA_HANG'
}

export enum PaymentStatus {
  CHUA_THANH_TOAN = 'CHUA_THANH_TOAN',
  DA_THANH_TOAN = 'DA_THANH_TOAN',
  THANH_TOAN_MOT_PHAN = 'THANH_TOAN_MOT_PHAN',
  HOAN_TIEN = 'HOAN_TIEN'
}

export enum OrderType {
  ONLINE = 'ONLINE',
  TAI_QUAY = 'TAI_QUAY'
}

export interface OrderSearchFilters {
  search?: string
  trangThaiDonHang?: OrderStatus
  trangThaiThanhToan?: PaymentStatus
  loaiHoaDon?: OrderType
  khachHangId?: number
  nhanVienId?: number
  tuNgay?: string
  denNgay?: string
  minAmount?: number
  maxAmount?: number
}

export const useOrder = () => {
  const { get, post, put, execute } = useApi()
  const toast = useToast()

  /**
   * Get all orders with pagination and filtering
   */
  const getAllOrders = async (params: any = {}): Promise<{ content: Order[], totalElements: number }> => {
    const { data, error } = await execute(
      () => get<{ content: Order[], totalElements: number }>('/hoa-don', { params }),
      { errorContext: 'Lấy danh sách đơn hàng' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || { content: [], totalElements: 0 }
  }

  /**
   * Get order by ID
   */
  const getOrderById = async (id: number): Promise<Order | null> => {
    const { data, error } = await execute(
      () => get<Order>(`/hoa-don/${id}`),
      { errorContext: 'Lấy thông tin đơn hàng' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || null
  }

  /**
   * Create new order
   */
  const createOrder = async (order: Order): Promise<Order> => {
    const { data, error } = await execute(
      () => post<Order>('/hoa-don', order),
      { 
        successMessage: 'Tạo đơn hàng thành công',
        showSuccessToast: true,
        errorContext: 'Tạo đơn hàng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Update order
   */
  const updateOrder = async (id: number, order: Order): Promise<Order> => {
    const { data, error } = await execute(
      () => put<Order>(`/hoa-don/${id}`, order),
      { 
        successMessage: 'Cập nhật đơn hàng thành công',
        showSuccessToast: true,
        errorContext: 'Cập nhật đơn hàng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Cancel order
   */
  const cancelOrder = async (id: number, reason?: string): Promise<Order> => {
    const { data, error } = await execute(
      () => post<Order>(`/hoa-don/${id}/cancel`, null, { params: { reason } }),
      { 
        successMessage: 'Hủy đơn hàng thành công',
        showSuccessToast: true,
        errorContext: 'Hủy đơn hàng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Confirm payment
   */
  const confirmPayment = async (id: number, paymentMethod: string): Promise<Order> => {
    const { data, error } = await execute(
      () => post<Order>(`/hoa-don/${id}/confirm-payment`, null, { params: { phuongThucThanhToan: paymentMethod } }),
      { 
        successMessage: 'Xác nhận thanh toán thành công',
        showSuccessToast: true,
        errorContext: 'Xác nhận thanh toán'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Get order audit history
   */
  const getOrderHistory = async (id: number): Promise<TimelineEntry[]> => {
    const { data, error } = await execute(
      () => get<TimelineEntry[]>(`/hoa-don/${id}/history`),
      { errorContext: 'Lấy lịch sử đơn hàng' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Search orders with filters
   */
  const searchOrders = async (filters: OrderSearchFilters, page: number = 0, size: number = 10): Promise<{ content: Order[], totalElements: number }> => {
    const params = {
      page,
      size,
      ...filters
    }

    return getAllOrders(params)
  }

  /**
   * Update order status
   */
  const updateOrderStatus = async (id: number, status: OrderStatus, reason?: string): Promise<Order> => {
    const { data, error } = await execute(
      () => post<Order>(`/hoa-don/${id}/update-status`, { trangThaiDonHang: status, lyDo: reason }),
      {
        successMessage: 'Cập nhật trạng thái đơn hàng thành công',
        showSuccessToast: true,
        errorContext: 'Cập nhật trạng thái đơn hàng'
      }
    )

    if (error) {
      throw new Error(error.message)
    }

    return data!
  }

  /**
   * Get order status display text
   */
  const getOrderStatusText = (status: OrderStatus): string => {
    const statusMap = {
      [OrderStatus.CHO_XAC_NHAN]: 'Chờ xác nhận',
      [OrderStatus.DA_XAC_NHAN]: 'Đã xác nhận',
      [OrderStatus.DANG_CHUAN_BI]: 'Đang chuẩn bị',
      [OrderStatus.DANG_GIAO_HANG]: 'Đang giao hàng',
      [OrderStatus.DA_GIAO_HANG]: 'Đã giao hàng',
      [OrderStatus.DA_HUY]: 'Đã hủy',
      [OrderStatus.TRA_HANG]: 'Trả hàng'
    }
    return statusMap[status] || status
  }

  /**
   * Get payment status display text
   */
  const getPaymentStatusText = (status: PaymentStatus): string => {
    const statusMap = {
      [PaymentStatus.CHUA_THANH_TOAN]: 'Chưa thanh toán',
      [PaymentStatus.DA_THANH_TOAN]: 'Đã thanh toán',
      [PaymentStatus.THANH_TOAN_MOT_PHAN]: 'Thanh toán một phần',
      [PaymentStatus.HOAN_TIEN]: 'Hoàn tiền'
    }
    return statusMap[status] || status
  }

  /**
   * Get order type display text
   */
  const getOrderTypeText = (type: OrderType): string => {
    const typeMap = {
      [OrderType.ONLINE]: 'Trực tuyến',
      [OrderType.TAI_QUAY]: 'Tại quầy'
    }
    return typeMap[type] || type
  }

  /**
   * Get order status severity for UI
   */
  const getOrderStatusSeverity = (status: OrderStatus): 'success' | 'info' | 'warn' | 'danger' => {
    const severityMap = {
      [OrderStatus.CHO_XAC_NHAN]: 'warn' as const,
      [OrderStatus.DA_XAC_NHAN]: 'info' as const,
      [OrderStatus.DANG_CHUAN_BI]: 'info' as const,
      [OrderStatus.DANG_GIAO_HANG]: 'info' as const,
      [OrderStatus.DA_GIAO_HANG]: 'success' as const,
      [OrderStatus.DA_HUY]: 'danger' as const,
      [OrderStatus.TRA_HANG]: 'warn' as const
    }
    return severityMap[status] || 'info'
  }

  /**
   * Get payment status severity for UI
   */
  const getPaymentStatusSeverity = (status: PaymentStatus): 'success' | 'info' | 'warn' | 'danger' => {
    const severityMap = {
      [PaymentStatus.CHUA_THANH_TOAN]: 'warn' as const,
      [PaymentStatus.DA_THANH_TOAN]: 'success' as const,
      [PaymentStatus.THANH_TOAN_MOT_PHAN]: 'info' as const,
      [PaymentStatus.HOAN_TIEN]: 'danger' as const
    }
    return severityMap[status] || 'info'
  }

  return {
    getAllOrders,
    getOrderById,
    createOrder,
    updateOrder,
    cancelOrder,
    confirmPayment,
    getOrderHistory,
    searchOrders,
    updateOrderStatus,
    getOrderStatusText,
    getPaymentStatusText,
    getOrderTypeText,
    getOrderStatusSeverity,
    getPaymentStatusSeverity
  }
}
