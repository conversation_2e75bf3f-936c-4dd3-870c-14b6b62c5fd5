/**
 * Voucher management composable for LapXpert Admin Dashboard
 * Provides comprehensive voucher CRUD operations and validation
 */

export interface Voucher {
  id?: number
  maPhieuGiamGia: string
  loaiGiamGia: 'PHAN_TRAM' | 'SO_TIEN_CO_DINH'
  trangThai: 'CHUA_DIEN_RA' | 'DA_DIEN_RA' | 'DA_KET_THUC' | 'DA_HUY'
  giaTriGiam: number
  giaTriDonHangToiThieu?: number
  ngayBatDau: string
  ngayKetThuc: string
  moTa?: string
  soLuongBanDau: number
  soLuongDaDung: number
  ngayTao?: string
  ngayCapNhat?: string
  danhSachNguoiDung?: number[]
  lyDoThayDoi?: string
}

export interface VoucherValidationRequest {
  voucherCode: string
  customerId?: number
  orderTotal: number
}

export interface VoucherValidationResponse {
  valid: boolean
  errorMessage?: string
  voucher?: Voucher
  discountAmount?: number
}

export interface VoucherFilters {
  status: string
  type: string
  dateRange: [Date, Date] | null
  search: string
  customerType: 'all' | 'public' | 'private'
}

export interface VoucherStats {
  totalVouchers: number
  activeVouchers: number
  expiredVouchers: number
  totalUsage: number
  totalDiscount: number
}

export const useVoucher = () => {
  const { get, post, put, delete: del, execute } = useApi()
  const toast = useToast()

  // State
  const vouchers = ref<Voucher[]>([])
  const currentVoucher = ref<Voucher | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const validationResults = ref<Record<string, VoucherValidationResponse>>({})
  const appliedVouchers = ref<Array<{ voucher: Voucher; discountAmount: number }>>([])

  // Filters and search
  const filters = ref<VoucherFilters>({
    status: 'all',
    type: 'all',
    dateRange: null,
    search: '',
    customerType: 'all'
  })

  const searchQuery = ref('')
  const searchResults = ref<Voucher[]>([])
  const searchLoading = ref(false)

  // Computed properties
  const filteredVouchers = computed(() => {
    let result = vouchers.value

    // Filter by status
    if (filters.value.status !== 'all') {
      result = result.filter(voucher => voucher.trangThai === filters.value.status)
    }

    // Filter by type (percentage vs fixed amount)
    if (filters.value.type !== 'all') {
      result = result.filter(voucher => voucher.loaiGiamGia === filters.value.type)
    }

    // Filter by customer type (public vs private)
    if (filters.value.customerType !== 'all') {
      if (filters.value.customerType === 'public') {
        result = result.filter(voucher => !voucher.danhSachNguoiDung || voucher.danhSachNguoiDung.length === 0)
      } else if (filters.value.customerType === 'private') {
        result = result.filter(voucher => voucher.danhSachNguoiDung && voucher.danhSachNguoiDung.length > 0)
      }
    }

    // Filter by date range
    if (filters.value.dateRange) {
      const [startDate, endDate] = filters.value.dateRange
      result = result.filter(voucher => {
        const voucherStart = new Date(voucher.ngayBatDau)
        const voucherEnd = new Date(voucher.ngayKetThuc)
        return voucherStart >= startDate && voucherEnd <= endDate
      })
    }

    // Filter by search query
    if (filters.value.search) {
      const query = filters.value.search.toLowerCase()
      result = result.filter(voucher =>
        voucher.maPhieuGiamGia.toLowerCase().includes(query) ||
        (voucher.moTa && voucher.moTa.toLowerCase().includes(query))
      )
    }

    return result
  })

  const voucherStats = computed((): VoucherStats => {
    const total = vouchers.value.length
    const active = vouchers.value.filter(v => v.trangThai === 'DA_DIEN_RA').length
    const expired = vouchers.value.filter(v => v.trangThai === 'DA_KET_THUC').length
    const totalUsage = vouchers.value.reduce((sum, v) => sum + v.soLuongDaDung, 0)
    const totalDiscount = vouchers.value.reduce((sum, v) => {
      // Estimate total discount based on usage and average discount
      return sum + (v.soLuongDaDung * v.giaTriGiam)
    }, 0)

    return {
      totalVouchers: total,
      activeVouchers: active,
      expiredVouchers: expired,
      totalUsage,
      totalDiscount
    }
  })

  const getTotalDiscount = computed(() => {
    return appliedVouchers.value.reduce((total, item) => total + item.discountAmount, 0)
  })

  const getAppliedVoucherCodes = computed(() => {
    return appliedVouchers.value.map(item => item.voucher.maPhieuGiamGia)
  })

  // Status mapping for display
  const voucherStatusMap = {
    'CHUA_DIEN_RA': { label: 'Chưa diễn ra', severity: 'info' },
    'DA_DIEN_RA': { label: 'Đang diễn ra', severity: 'success' },
    'DA_KET_THUC': { label: 'Đã kết thúc', severity: 'warning' },
    'DA_HUY': { label: 'Đã hủy', severity: 'danger' }
  }

  const voucherTypeMap = {
    'PHAN_TRAM': { label: 'Phần trăm', icon: 'pi pi-percentage' },
    'SO_TIEN_CO_DINH': { label: 'Số tiền cố định', icon: 'pi pi-dollar' }
  }

  // API Methods
  const fetchVouchers = async () => {
    loading.value = true
    error.value = null

    const { data, error: apiError } = await execute(
      () => get<Voucher[]>('/phieu-giam-gia'),
      { errorContext: 'Lấy danh sách voucher' }
    )

    if (data) {
      vouchers.value = data
    } else if (apiError) {
      error.value = apiError.message
    }

    loading.value = false
  }

  const fetchVoucherById = async (id: number) => {
    loading.value = true
    error.value = null

    const { data, error: apiError } = await execute(
      () => get<Voucher>(`/phieu-giam-gia/${id}`),
      { errorContext: 'Lấy thông tin voucher' }
    )

    if (data) {
      currentVoucher.value = data
    } else if (apiError) {
      error.value = apiError.message
    }

    loading.value = false
    return data
  }

  const createVoucher = async (voucher: Omit<Voucher, 'id'>) => {
    loading.value = true
    error.value = null

    const { data, error: apiError } = await execute(
      () => post<Voucher>('/phieu-giam-gia', voucher),
      { 
        successMessage: 'Tạo voucher thành công',
        errorContext: 'Tạo voucher',
        showSuccessToast: true
      }
    )

    if (data) {
      vouchers.value.push(data)
      currentVoucher.value = data
    } else if (apiError) {
      error.value = apiError.message
    }

    loading.value = false
    return data
  }

  const updateVoucher = async (id: number, voucher: Partial<Voucher>) => {
    loading.value = true
    error.value = null

    const { data, error: apiError } = await execute(
      () => put<Voucher>('/phieu-giam-gia', { ...voucher, id }),
      { 
        successMessage: 'Cập nhật voucher thành công',
        errorContext: 'Cập nhật voucher',
        showSuccessToast: true
      }
    )

    if (data) {
      const index = vouchers.value.findIndex(v => v.id === id)
      if (index !== -1) {
        vouchers.value[index] = data
      }
      currentVoucher.value = data
    } else if (apiError) {
      error.value = apiError.message
    }

    loading.value = false
    return data
  }

  const deleteVoucher = async (id: number, reason?: string) => {
    loading.value = true
    error.value = null

    const { data, error: apiError } = await execute(
      () => del(`/phieu-giam-gia/delete/${id}${reason ? `?reason=${encodeURIComponent(reason)}` : ''}`),
      { 
        successMessage: 'Xóa voucher thành công',
        errorContext: 'Xóa voucher',
        showSuccessToast: true
      }
    )

    if (!apiError) {
      vouchers.value = vouchers.value.filter(v => v.id !== id)
      if (currentVoucher.value?.id === id) {
        currentVoucher.value = null
      }
    } else {
      error.value = apiError.message
    }

    loading.value = false
    return !apiError
  }

  const validateVoucher = async (request: VoucherValidationRequest) => {
    const { data, error: apiError } = await execute(
      () => post<VoucherValidationResponse>('/phieu-giam-gia/validate', request),
      { errorContext: 'Xác thực voucher' }
    )

    if (data) {
      validationResults.value[request.voucherCode] = data
    }

    return data
  }

  const searchVouchers = async (query: string) => {
    if (!query.trim()) {
      searchResults.value = []
      return
    }

    searchLoading.value = true
    const { data } = await execute(
      () => get<Voucher[]>(`/phieu-giam-gia/search?q=${encodeURIComponent(query)}`),
      { errorContext: 'Tìm kiếm voucher' }
    )

    if (data) {
      searchResults.value = data
    }
    searchLoading.value = false
  }

  const getAvailableVouchers = async (customerId?: number, orderTotal: number = 0) => {
    const { data } = await execute(
      () => get<Voucher[]>(`/phieu-giam-gia/available?${customerId ? `customerId=${customerId}&` : ''}orderTotal=${orderTotal}`),
      { errorContext: 'Lấy voucher khả dụng' }
    )

    return data || []
  }

  const findBestVoucher = async (customerId?: number, orderTotal: number = 0) => {
    const { data } = await execute(
      () => get(`/phieu-giam-gia/best?${customerId ? `customerId=${customerId}&` : ''}orderTotal=${orderTotal}`),
      { errorContext: 'Tìm voucher tốt nhất' }
    )

    return data
  }

  const addVoucherToOrder = (voucher: Voucher, discountAmount: number) => {
    const existingIndex = appliedVouchers.value.findIndex(v => v.voucher.maPhieuGiamGia === voucher.maPhieuGiamGia)

    if (existingIndex !== -1) {
      appliedVouchers.value[existingIndex] = { voucher, discountAmount }
    } else {
      appliedVouchers.value.push({ voucher, discountAmount })
    }

    toast.add({
      severity: 'success',
      summary: 'Thành công',
      detail: `Voucher ${voucher.maPhieuGiamGia} đã được áp dụng`,
      life: 3000
    })
  }

  const removeVoucherFromOrder = (voucherCode: string) => {
    const index = appliedVouchers.value.findIndex(v => v.voucher.maPhieuGiamGia === voucherCode)
    if (index !== -1) {
      appliedVouchers.value.splice(index, 1)
      toast.add({
        severity: 'info',
        summary: 'Thông báo',
        detail: `Voucher ${voucherCode} đã được gỡ bỏ`,
        life: 3000
      })
    }
  }

  const clearAppliedVouchers = () => {
    appliedVouchers.value = []
  }

  const setFilters = (newFilters: Partial<VoucherFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {
      status: 'all',
      type: 'all',
      dateRange: null,
      search: '',
      customerType: 'all'
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearValidationCache = () => {
    validationResults.value = {}
  }

  const formatDiscountValue = (voucher: Voucher) => {
    if (!voucher) return ''

    if (voucher.loaiGiamGia === 'PHAN_TRAM') {
      return `${voucher.giaTriGiam}%`
    } else {
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(voucher.giaTriGiam)
    }
  }

  const calculateDiscountAmount = (voucher: Voucher, orderTotal: number) => {
    if (!voucher || !orderTotal) return 0

    if (voucher.loaiGiamGia === 'PHAN_TRAM') {
      return (orderTotal * voucher.giaTriGiam) / 100
    } else {
      return Math.min(voucher.giaTriGiam, orderTotal)
    }
  }

  const isVoucherApplied = (voucherCode: string) => {
    return appliedVouchers.value.some(item => item.voucher.maPhieuGiamGia === voucherCode)
  }

  const getVoucherByCode = (voucherCode: string) => {
    return vouchers.value.find(voucher => voucher.maPhieuGiamGia === voucherCode)
  }

  const getVoucherStatusInfo = (status: string) => {
    return voucherStatusMap[status as keyof typeof voucherStatusMap] || { label: status, severity: 'secondary' }
  }

  const generateVoucherCode = (prefix: string = 'VOUCHER') => {
    const timestamp = Date.now().toString().slice(-6)
    const random = Math.random().toString(36).substring(2, 6).toUpperCase()
    return `${prefix}${timestamp}${random}`
  }

  return {
    // State
    vouchers: readonly(vouchers),
    currentVoucher: readonly(currentVoucher),
    loading: readonly(loading),
    error: readonly(error),
    validationResults: readonly(validationResults),
    appliedVouchers: readonly(appliedVouchers),
    filters,
    searchQuery,
    searchResults: readonly(searchResults),
    searchLoading: readonly(searchLoading),

    // Computed
    filteredVouchers,
    voucherStats,
    getTotalDiscount,
    getAppliedVoucherCodes,
    voucherStatusMap,
    voucherTypeMap,

    // CRUD Methods
    fetchVouchers,
    fetchVoucherById,
    createVoucher,
    updateVoucher,
    deleteVoucher,

    // Validation & Search
    validateVoucher,
    searchVouchers,
    getAvailableVouchers,
    findBestVoucher,

    // Order Integration
    addVoucherToOrder,
    removeVoucherFromOrder,
    clearAppliedVouchers,

    // Utility Methods
    setFilters,
    clearFilters,
    clearError,
    clearValidationCache,
    formatDiscountValue,
    calculateDiscountAmount,
    isVoucherApplied,
    getVoucherByCode,
    getVoucherStatusInfo,
    generateVoucherCode
  }
}
