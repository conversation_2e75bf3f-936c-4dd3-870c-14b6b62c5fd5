/**
 * Product composable for LapXpert Admin Dashboard
 * Provides product management functionality including CRUD operations
 */

export interface Product {
  id?: number
  maSanPham?: string
  tenSanPham: string
  moTa?: string
  hinhAnh?: string[]
  ngayRaMat?: string
  trangThai?: boolean
  thuongHieu?: Brand
  danhMucs?: Category[]
  sanPhamChiTiets?: ProductVariant[]
  // Fixed field names to match backend DTO
  ngayTao?: string // Was: createdAt
  ngayCapNhat?: string // Was: updatedAt
  nguoiTao?: string
  nguoiCapNhat?: string
}

export interface ProductVariant {
  id?: number
  sku?: string
  giaBan: number
  giaKhuyenMai?: number
  // Note: soLuongTon not in backend DTO but needed for frontend display
  soLuongTon?: number
  trangThai?: boolean
  hinhAnh?: string[] // Images for this variant
  cpu?: Cpu
  ram?: Ram
  gpu?: Gpu
  mauSac?: Color
  oCung?: Storage
  manHinh?: Screen
  ngayTao?: string
  ngayCapNhat?: string
}

export interface Brand {
  id?: number
  maThuongHieu?: string
  moTaThuongHieu: string
  trangThai?: boolean
}

export interface Category {
  id?: number
  maDanhMuc?: string
  tenDanhMuc: string
  moTa?: string
  trangThai?: boolean
}

export interface Cpu {
  id?: number
  maCpu?: string
  tenCpu: string
  trangThai?: boolean
}

export interface Ram {
  id?: number
  maRam?: string
  dungLuongRam: string
  trangThai?: boolean
}

export interface Gpu {
  id?: number
  maGpu?: string
  tenGpu: string
  trangThai?: boolean
}

export interface Color {
  id?: number
  maMauSac?: string
  tenMauSac: string
  trangThai?: boolean
}

export interface Storage {
  id?: number
  maOCung?: string
  dungLuongOCung: string
  loaiOCung?: string
  trangThai?: boolean
}

export interface Screen {
  id?: number
  maManHinh?: string
  kichThuocManHinh: string
  doPhanGiai?: string
  trangThai?: boolean
}

export interface ProductSearchFilters {
  tenSanPham?: string
  thuongHieuId?: number
  danhMucIds?: number[]
  trangThai?: boolean
  minPrice?: number
  maxPrice?: number
}

export const useProduct = () => {
  const { get, post, put, delete: del, execute } = useApi()
  const toast = useToast()

  /**
   * Get all products
   */
  const getAllProducts = async (): Promise<Product[]> => {
    const { data, error } = await execute(
      () => get<Product[]>('/products'),
      { errorContext: 'Lấy danh sách sản phẩm' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Get active products only
   */
  const getActiveProducts = async (): Promise<Product[]> => {
    const { data, error } = await execute(
      () => get<Product[]>('/products/list'),
      { errorContext: 'Lấy danh sách sản phẩm hoạt động' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Get product by ID
   */
  const getProductById = async (id: number): Promise<Product | null> => {
    const { data, error } = await execute(
      () => get<Product>(`/products/${id}`),
      { errorContext: 'Lấy thông tin sản phẩm' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || null
  }

  /**
   * Create new product
   */
  const createProduct = async (product: Product): Promise<Product> => {
    const { data, error } = await execute(
      () => post<Product>('/products/add', product),
      { 
        successMessage: 'Thêm sản phẩm thành công',
        showSuccessToast: true,
        errorContext: 'Thêm sản phẩm'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Update product
   */
  const updateProduct = async (id: number, product: Product): Promise<Product> => {
    const { data, error } = await execute(
      () => put<Product>(`/products/update/${id}`, product),
      { 
        successMessage: 'Cập nhật sản phẩm thành công',
        showSuccessToast: true,
        errorContext: 'Cập nhật sản phẩm'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Update product with variants
   */
  const updateProductWithVariants = async (id: number, product: Product): Promise<Product> => {
    const { data, error } = await execute(
      () => put<Product>(`/products/updateWithVariants/${id}`, product),
      { 
        successMessage: 'Cập nhật sản phẩm và biến thể thành công',
        showSuccessToast: true,
        errorContext: 'Cập nhật sản phẩm với biến thể'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Soft delete product
   */
  const deleteProduct = async (id: number): Promise<void> => {
    const { error } = await execute(
      () => del(`/products/delete/${id}`),
      { 
        successMessage: 'Xóa sản phẩm thành công',
        showSuccessToast: true,
        errorContext: 'Xóa sản phẩm'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Search products with filters
   */
  const searchProducts = async (filters: ProductSearchFilters): Promise<Product[]> => {
    const { data, error } = await execute(
      () => post<Product[]>('/products/search', filters),
      { errorContext: 'Tìm kiếm sản phẩm' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Get product audit history
   */
  const getProductAuditHistory = async (id: number) => {
    const { data, error } = await execute(
      () => get(`/products/${id}/audit-history`),
      { errorContext: 'Lấy lịch sử thay đổi sản phẩm' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  return {
    getAllProducts,
    getActiveProducts,
    getProductById,
    createProduct,
    updateProduct,
    updateProductWithVariants,
    deleteProduct,
    searchProducts,
    getProductAuditHistory
  }
}
