/**
 * Statistics composable for LapXpert Admin Dashboard
 * Provides comprehensive business intelligence and reporting functionality
 * Integrates with ThongKeController backend APIs
 */

export interface DashboardSummary {
  totalRevenue: number
  totalOrders: number
  totalProducts: number
  totalCustomers: number
  revenueGrowth: number
  ordersGrowth: number
  productsGrowth: number
  customersGrowth: number
}

export interface RevenueData {
  labels: string[]
  data: number[]
  growth: number
  bestDay?: string
  bestAmount?: number
}

export interface OrderStats {
  total: number
  completed: number
  processing: number
  cancelled: number
  averageValue: number
  growth: number
}

export interface ProductStats {
  id: number
  tenSanPham: string
  hinhAnh: string
  thuongHieu: string
  soLuongBan: number
  doanhThu: number
  rank: number
  percentage: number
}

export interface CustomerStats {
  newCustomers: number
  returningCustomers: number
  retentionRate: number
  averageValue: number
  growth: number
}

export const useStatistics = () => {
  const { get, execute } = useApi()
  const toast = useToast()

  // State
  const loading = ref(false)
  const error = ref<string | null>(null)

  // ==================== DASHBOARD SUMMARY ====================

  /**
   * Get dashboard summary with key metrics
   */
  const getDashboardSummary = async (): Promise<DashboardSummary> => {
    const { data, error: apiError } = await execute(
      () => get<DashboardSummary>('/thong-ke/dashboard'),
      { errorContext: 'Lấy tổng quan dashboard' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data || {
      totalRevenue: 0,
      totalOrders: 0,
      totalProducts: 0,
      totalCustomers: 0,
      revenueGrowth: 0,
      ordersGrowth: 0,
      productsGrowth: 0,
      customersGrowth: 0
    }
  }

  // ==================== REVENUE STATISTICS ====================

  /**
   * Get daily revenue statistics
   */
  const getDailyRevenue = async (startDate?: string, endDate?: string): Promise<RevenueData> => {
    const params: any = {}
    if (startDate) params.tuNgay = startDate
    if (endDate) params.denNgay = endDate

    const { data, error: apiError } = await execute(
      () => get<RevenueData>('/thong-ke/doanh-thu/theo-ngay', { params }),
      { errorContext: 'Lấy doanh thu theo ngày' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data || { labels: [], data: [], growth: 0 }
  }

  /**
   * Get monthly revenue statistics
   */
  const getMonthlyRevenue = async (year?: number): Promise<RevenueData> => {
    const params: any = {}
    if (year) params.nam = year

    const { data, error: apiError } = await execute(
      () => get<RevenueData>('/thong-ke/doanh-thu/theo-thang', { params }),
      { errorContext: 'Lấy doanh thu theo tháng' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data || { labels: [], data: [], growth: 0 }
  }

  /**
   * Get revenue overview/summary
   */
  const getRevenueOverview = async () => {
    const { data, error: apiError } = await execute(
      () => get('/thong-ke/doanh-thu/tong-quan'),
      { errorContext: 'Lấy tổng quan doanh thu' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data
  }

  // ==================== ORDER STATISTICS ====================

  /**
   * Get order statistics by status
   */
  const getOrdersByStatus = async (): Promise<OrderStats> => {
    const { data, error: apiError } = await execute(
      () => get<OrderStats>('/thong-ke/don-hang/theo-trang-thai'),
      { errorContext: 'Lấy thống kê đơn hàng theo trạng thái' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data || {
      total: 0,
      completed: 0,
      processing: 0,
      cancelled: 0,
      averageValue: 0,
      growth: 0
    }
  }

  /**
   * Get average order value statistics
   */
  const getAverageOrderValue = async () => {
    const { data, error: apiError } = await execute(
      () => get('/thong-ke/don-hang/gia-tri-trung-binh'),
      { errorContext: 'Lấy giá trị đơn hàng trung bình' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data
  }

  // ==================== PRODUCT STATISTICS ====================

  /**
   * Get top selling products
   */
  const getTopSellingProducts = async (
    limit: number = 10,
    startDate?: string,
    endDate?: string
  ): Promise<ProductStats[]> => {
    const params: any = { soLuong: limit }
    if (startDate) params.tuNgay = startDate
    if (endDate) params.denNgay = endDate

    const { data, error: apiError } = await execute(
      () => get<{ sanPhamList: ProductStats[] }>('/thong-ke/san-pham/ban-chay-nhat', { params }),
      { errorContext: 'Lấy sản phẩm bán chạy nhất' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data?.sanPhamList || []
  }

  /**
   * Get low stock products
   */
  const getLowStockProducts = async (threshold: number = 10) => {
    const { data, error: apiError } = await execute(
      () => get('/thong-ke/san-pham/sap-het-hang', { params: { nguongTonKho: threshold } }),
      { errorContext: 'Lấy sản phẩm sắp hết hàng' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data
  }

  /**
   * Get products by category
   */
  const getProductsByCategory = async () => {
    const { data, error: apiError } = await execute(
      () => get('/thong-ke/san-pham/theo-danh-muc'),
      { errorContext: 'Lấy sản phẩm theo danh mục' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data
  }

  // ==================== CUSTOMER STATISTICS ====================

  /**
   * Get new customer statistics
   */
  const getNewCustomers = async (startDate?: string, endDate?: string): Promise<CustomerStats> => {
    const params: any = {}
    if (startDate) params.tuNgay = startDate
    if (endDate) params.denNgay = endDate

    const { data, error: apiError } = await execute(
      () => get<CustomerStats>('/thong-ke/khach-hang/moi', { params }),
      { errorContext: 'Lấy thống kê khách hàng mới' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data || {
      newCustomers: 0,
      returningCustomers: 0,
      retentionRate: 0,
      averageValue: 0,
      growth: 0
    }
  }

  /**
   * Get customer retention rate
   */
  const getCustomerRetention = async () => {
    const { data, error: apiError } = await execute(
      () => get('/thong-ke/khach-hang/ty-le-giu-chan'),
      { errorContext: 'Lấy tỷ lệ giữ chân khách hàng' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data
  }

  /**
   * Get average customer value
   */
  const getAverageCustomerValue = async () => {
    const { data, error: apiError } = await execute(
      () => get('/thong-ke/khach-hang/gia-tri-trung-binh'),
      { errorContext: 'Lấy giá trị khách hàng trung bình' }
    )

    if (apiError) {
      throw new Error(apiError.message)
    }

    return data
  }

  return {
    // State
    loading,
    error,

    // Dashboard
    getDashboardSummary,

    // Revenue
    getDailyRevenue,
    getMonthlyRevenue,
    getRevenueOverview,

    // Orders
    getOrdersByStatus,
    getAverageOrderValue,

    // Products
    getTopSellingProducts,
    getLowStockProducts,
    getProductsByCategory,

    // Customers
    getNewCustomers,
    getCustomerRetention,
    getAverageCustomerValue
  }
}
